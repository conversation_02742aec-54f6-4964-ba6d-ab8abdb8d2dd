@echo off
echo ========================================
echo   Simple UMDF Concept Test
echo ========================================
echo.

if not exist build mkdir build

echo Compiling simple UMDF test...

REM Try MinGW first
g++.exe --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW G++ Compiler
    g++ -std=c++17 -DWIN32 -D_WINDOWS ^
        -I include ^
        src/SimpleUMDFTest.cpp ^
        -o build/SimpleUMDFTest.exe ^
        -static-libgcc -static-libstdc++
    goto :check_result
)

REM Try MSVC
cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using Microsoft Visual C++ Compiler
    cl /EHsc /std:c++17 /DWIN32 /D_WINDOWS ^
       /I include ^
       src/SimpleUMDFTest.cpp ^
       /Fe:build/SimpleUMDFTest.exe
    goto :check_result
)

echo ERROR: No C++ compiler found!
pause
exit /b 1

:check_result
if exist build\SimpleUMDFTest.exe (
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo ========================================
    echo.
    echo Running test...
    echo.
    build\SimpleUMDFTest.exe
) else (
    echo.
    echo ========================================
    echo Build FAILED!
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
