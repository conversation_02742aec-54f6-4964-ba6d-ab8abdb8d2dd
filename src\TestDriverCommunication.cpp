#include <windows.h>
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include "RS485Types.h"

/**
 * @brief Test communication with the UMDF driver
 */

// IOCTL codes (same as in driver)
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HARDWARE_STATUS CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)

// IOCTL structures (same as in driver)
typedef struct _RS485_COMMAND_INPUT {
    CHAR CommandKey[4];
    UINT64 Value;
    UINT8 SlaveAddress;
    UINT8 Reserved[3];
} RS485_COMMAND_INPUT;

typedef struct _RS485_RESPONSE_OUTPUT {
    UINT8 SlaveAddress;
    UINT8 PayloadData[12];
    UINT32 DataLength;
    UINT8 Reserved[3];
} RS485_RESPONSE_OUTPUT;

/**
 * @brief Test driver communication using LoadLibrary (for testing)
 */
class DriverTester {
private:
    HMODULE m_hDriverDll;
    typedef DWORD (*ProcessDriverIoControlFunc)(DWORD, const void*, DWORD, void*, DWORD, DWORD*);
    ProcessDriverIoControlFunc m_pProcessIoControl;

public:
    DriverTester() : m_hDriverDll(nullptr), m_pProcessIoControl(nullptr) {}

    ~DriverTester() {
        if (m_hDriverDll) {
            FreeLibrary(m_hDriverDll);
        }
    }

    bool Initialize() {
        // Load the driver DLL for testing
        m_hDriverDll = LoadLibraryA("build\\driver\\RS485Filter.dll");
        if (!m_hDriverDll) {
            std::cout << "Failed to load driver DLL. Error: " << GetLastError() << std::endl;
            return false;
        }

        // Get the function pointer
        m_pProcessIoControl = (ProcessDriverIoControlFunc)GetProcAddress(m_hDriverDll, "ProcessDriverIoControl");
        if (!m_pProcessIoControl) {
            std::cout << "Failed to get ProcessDriverIoControl function. Error: " << GetLastError() << std::endl;
            return false;
        }

        std::cout << "✓ Driver DLL loaded successfully!" << std::endl;
        return true;
    }

    DWORD SendIoControl(DWORD ioControlCode, const void* inputBuffer, DWORD inputSize,
                       void* outputBuffer, DWORD outputSize, DWORD* bytesReturned = nullptr) {
        if (!m_pProcessIoControl) {
            return ERROR_INVALID_HANDLE;
        }

        return m_pProcessIoControl(ioControlCode, inputBuffer, inputSize, 
                                  outputBuffer, outputSize, bytesReturned);
    }

    void TestSystemConfiguration() {
        std::cout << "\n=== Testing System Configuration (S-series) ===" << std::endl;

        // Test S001 command
        std::cout << "1. Testing S001 command (set slave address to 5)..." << std::endl;
        RS485_COMMAND_INPUT s001Input = {};
        strncpy(s001Input.CommandKey, "S001", 4);
        s001Input.Value = 5;
        s001Input.SlaveAddress = 0; // Broadcast

        DWORD result = SendIoControl(IOCTL_RS485_CONFIGURE_SYSTEM, &s001Input, sizeof(s001Input), nullptr, 0);
        std::cout << "   Result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;

        // Test S002 command
        std::cout << "2. Testing S002 command (set baud rate to 115200)..." << std::endl;
        RS485_COMMAND_INPUT s002Input = {};
        strncpy(s002Input.CommandKey, "S002", 4);
        s002Input.Value = 115200;
        s002Input.SlaveAddress = 0; // Broadcast

        result = SendIoControl(IOCTL_RS485_CONFIGURE_SYSTEM, &s002Input, sizeof(s002Input), nullptr, 0);
        std::cout << "   Result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;
    }

    void TestUserConfiguration() {
        std::cout << "\n=== Testing User Configuration (U-series) ===" << std::endl;

        // Test U001 command
        std::cout << "1. Testing U001 command (set SEL threshold to 250mA)..." << std::endl;
        RS485_COMMAND_INPUT u001Input = {};
        strncpy(u001Input.CommandKey, "U001", 4);
        u001Input.Value = 250;
        u001Input.SlaveAddress = 5; // Target slave 5

        DWORD result = SendIoControl(IOCTL_RS485_CONFIGURE_USER, &u001Input, sizeof(u001Input), nullptr, 0);
        std::cout << "   Result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;

        // Test U002 command
        std::cout << "2. Testing U002 command (set max amplitude to 1500mA)..." << std::endl;
        RS485_COMMAND_INPUT u002Input = {};
        strncpy(u002Input.CommandKey, "U002", 4);
        u002Input.Value = 1500;
        u002Input.SlaveAddress = 5;

        result = SendIoControl(IOCTL_RS485_CONFIGURE_USER, &u002Input, sizeof(u002Input), nullptr, 0);
        std::cout << "   Result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;
    }

    void TestDataRequest() {
        std::cout << "\n=== Testing Data Request (A-series) ===" << std::endl;

        // Test A001 command
        std::cout << "1. Testing A001 command (request SEL event log)..." << std::endl;
        RS485_COMMAND_INPUT a001Input = {};
        strncpy(a001Input.CommandKey, "A001", 4);
        a001Input.Value = 0;
        a001Input.SlaveAddress = 5;

        DWORD result = SendIoControl(IOCTL_RS485_REQUEST_DATA, &a001Input, sizeof(a001Input), nullptr, 0);
        std::cout << "   Request Result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;

        if (result == ERROR_SUCCESS) {
            // Try to receive response
            std::cout << "2. Receiving response..." << std::endl;
            RS485_RESPONSE_OUTPUT response = {};
            DWORD bytesReturned = 0;

            result = SendIoControl(IOCTL_RS485_RECEIVE_RESPONSE, nullptr, 0, &response, sizeof(response), &bytesReturned);
            if (result == ERROR_SUCCESS) {
                std::cout << "   Response received from slave " << static_cast<int>(response.SlaveAddress) << std::endl;
                std::cout << "   Data length: " << response.DataLength << " bytes" << std::endl;
                std::cout << "   Data: ";
                for (UINT32 i = 0; i < std::min(response.DataLength, 12U); i++) {
                    std::cout << std::hex << std::uppercase << std::setw(2) << std::setfill('0') 
                              << static_cast<int>(response.PayloadData[i]) << " ";
                }
                std::cout << std::dec << std::endl;
            } else {
                std::cout << "   No response available (Code: " << result << ")" << std::endl;
            }
        }
    }

    void TestBufferStatus() {
        std::cout << "\n=== Testing Buffer Status ===" << std::endl;

        BufferStatus status = {};
        DWORD bytesReturned = 0;

        DWORD result = SendIoControl(IOCTL_RS485_GET_BUFFER_STATUS, nullptr, 0, &status, sizeof(status), &bytesReturned);
        if (result == ERROR_SUCCESS) {
            std::cout << "✓ Buffer status retrieved successfully:" << std::endl;
            std::cout << "   Uplink: " << status.uplinkUsed << "/" << status.uplinkCapacity 
                      << " (" << std::fixed << std::setprecision(1) << status.uplinkUsagePercent << "%)" << std::endl;
            std::cout << "   Downlink: " << status.downlinkUsed << "/" << status.downlinkCapacity 
                      << " (" << std::fixed << std::setprecision(1) << status.downlinkUsagePercent << "%)" << std::endl;
            std::cout << "   Uplink full: " << (status.isUplinkFull ? "YES" : "NO") << std::endl;
            std::cout << "   Downlink full: " << (status.isDownlinkFull ? "YES" : "NO") << std::endl;
        } else {
            std::cout << "✗ Failed to get buffer status (Code: " << result << ")" << std::endl;
        }
    }

    void TestHardwareStatus() {
        std::cout << "\n=== Testing Hardware Status ===" << std::endl;

        HardwareStatus status = {};
        DWORD bytesReturned = 0;

        DWORD result = SendIoControl(IOCTL_RS485_GET_HARDWARE_STATUS, nullptr, 0, &status, sizeof(status), &bytesReturned);
        if (result == ERROR_SUCCESS) {
            std::cout << "✓ Hardware status retrieved successfully:" << std::endl;
            std::cout << "   Connected: " << (status.isConnected ? "YES" : "NO") << std::endl;
            std::cout << "   Driver loaded: " << (status.isDriverLoaded ? "YES" : "NO") << std::endl;
            std::cout << "   Signal strength: " << status.signalStrength << "%" << std::endl;
            std::cout << "   Error count: " << status.errorCount << std::endl;
            std::cout << "   Last activity: " << status.lastActivityTime << std::endl;
        } else {
            std::cout << "✗ Failed to get hardware status (Code: " << result << ")" << std::endl;
        }
    }

    void TestBufferClear() {
        std::cout << "\n=== Testing Buffer Clear ===" << std::endl;

        BufferType bufferType = BufferType::BOTH;
        DWORD result = SendIoControl(IOCTL_RS485_CLEAR_BUFFER, &bufferType, sizeof(bufferType), nullptr, 0);
        std::cout << "Clear buffers result: " << (result == ERROR_SUCCESS ? "SUCCESS" : "FAILED") << " (Code: " << result << ")" << std::endl;
    }
};

int main() {
    std::cout << "========================================" << std::endl;
    std::cout << "  UMDF Driver Communication Test" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;

    DriverTester tester;
    
    if (!tester.Initialize()) {
        std::cout << "Failed to initialize driver tester." << std::endl;
        std::cout << "Make sure the driver DLL is built and available." << std::endl;
        std::cout << "\nPress Enter to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    try {
        // Run all tests
        tester.TestSystemConfiguration();
        tester.TestUserConfiguration();
        tester.TestDataRequest();
        tester.TestBufferStatus();
        tester.TestHardwareStatus();
        tester.TestBufferClear();

        std::cout << "\n========================================" << std::endl;
        std::cout << "  All Tests Completed!" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        std::cout << "This demonstrates successful communication with the UMDF driver:" << std::endl;
        std::cout << "✓ S-series system configuration commands" << std::endl;
        std::cout << "✓ U-series user configuration commands" << std::endl;
        std::cout << "✓ A-series data request commands" << std::endl;
        std::cout << "✓ Buffer management and status queries" << std::endl;
        std::cout << "✓ Hardware status monitoring" << std::endl;
        std::cout << "✓ IOCTL-based driver communication" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error during testing: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\nPress Enter to exit..." << std::endl;
    std::cin.get();
    return 0;
}
