@echo off
echo ========================================
echo    RS485 Communication Application
echo         Build Script
echo ========================================
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if %ERRORLEVEL% NEQ 0 (
            echo ERROR: Visual Studio not found!
            echo Please install Visual Studio 2019 or 2022 with C++ development tools.
            pause
            exit /b 1
        )
    )
)

REM Check if CMake is available
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: CMake not found!
    echo Please install CMake and add it to your PATH.
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% NEQ 0 (
    echo Trying Visual Studio 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: CMake configuration failed!
        pause
        exit /b 1
    )
)

echo.
echo Building project...
cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo.
echo Output file: build\Release\RS485_Communication_Application.exe
echo ========================================
echo.

REM Check if the executable was created
if exist "Release\RS485_Communication_Application.exe" (
    echo Executable created successfully.
    echo Size: 
    dir "Release\RS485_Communication_Application.exe" | find "RS485_Communication_Application.exe"
) else (
    echo WARNING: Executable not found in expected location.
    echo Please check the build output for errors.
)

echo.
echo Press any key to exit...
pause >nul
