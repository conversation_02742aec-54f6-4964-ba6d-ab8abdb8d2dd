#ifndef RS485_DRIVER_H
#define RS485_DRIVER_H

#include "RS485Types.h"
#include "RS485DataFormat.h"
#include "CRC8Calculator.h"
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <condition_variable>

/**
 * @brief Main RS485 Driver Interface
 * 
 * This class provides the high-level API for RS485 communication with AI-SLDAP devices.
 * It implements the five API categories as specified in the design document:
 * 1. Error Handle API (Management functions)
 * 2. Master Broadcasting API (S-series commands)
 * 3. Master Assign Data API (U-series and W-series commands)
 * 4. Master Request API (A-series commands)
 * 5. Slave Response API (Response handling)
 */
class RS485Driver {
public:
    /**
     * @brief Constructor
     */
    RS485Driver();

    /**
     * @brief Destructor
     */
    ~RS485Driver();

    // ===== ERROR HANDLE API (Function Code: 0b000) =====
    
    /**
     * @brief Open RS485 communication port
     * @param devicePath Device path or COM port name (e.g., "COM3")
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error openPort(const std::wstring& devicePath);

    /**
     * @brief Close RS485 communication port
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error closePort();

    /**
     * @brief Check if port is currently open
     * @return true if port is open and ready for communication
     */
    bool isPortOpen() const;

    /**
     * @brief Get port information
     * @param info Output structure for port information
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getPortInfo(PortInfo& info);

    /**
     * @brief Enumerate available RS485 devices
     * @param deviceList Output vector for device information
     * @return RS485Error::SUCCESS if successful
     */
    static RS485Error enumerateDevices(std::vector<DeviceInfo>& deviceList);

    /**
     * @brief Detect multiple devices on the bus
     * @param detectedAddresses Output vector for detected slave addresses
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);

    /**
     * @brief Get buffer status
     * @param status Output structure for buffer status
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getBufferStatus(BufferStatus& status);

    /**
     * @brief Check uplink buffer flag
     * @param isFull Output flag indicating if uplink buffer is full
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error checkUplinkBufferFlag(bool& isFull);

    /**
     * @brief Check downlink buffer flag
     * @param isFull Output flag indicating if downlink buffer is full
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error checkDownlinkBufferFlag(bool& isFull);

    /**
     * @brief Clear communication buffers
     * @param bufferType Type of buffer to clear (uplink, downlink, or both)
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error clearBuffer(BufferType bufferType = BufferType::BOTH);

    /**
     * @brief Set buffer overflow policy
     * @param policy Policy to apply when buffers are full
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error setBufferOverflowPolicy(BufferOverflowPolicy policy);

    /**
     * @brief Get buffer capacity information
     * @param uplinkFrames Output for uplink buffer capacity
     * @param downlinkFrames Output for downlink buffer capacity
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

    /**
     * @brief Get hardware status
     * @param status Output structure for hardware status
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getHardwareStatus(HardwareStatus& status);

    /**
     * @brief Get performance metrics
     * @param metrics Output structure for performance metrics
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getPerformanceMetrics(PerformanceMetrics& metrics);

    /**
     * @brief Get current baud rate
     * @param currentBaudRate Output for current baud rate
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getBaudRate(uint32_t& currentBaudRate);

    /**
     * @brief Get line status
     * @param status Output structure for line status
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getLineStatus(LineStatus& status);

    /**
     * @brief Get error description string
     * @param error Error code
     * @return Human-readable error description
     */
    const char* getErrorString(RS485Error error) const;

    /**
     * @brief Register error callback function
     * @param callback Function to call when errors occur
     */
    void registerErrorCallback(ErrorCallbackFn callback);

    /**
     * @brief Register buffer threshold callback
     * @param callback Function to call when buffer threshold is reached
     */
    void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

    // ===== MASTER BROADCASTING API (Function Code: 0b111) =====
    
    /**
     * @brief Configure system settings (S-series commands)
     * @param commandKey Command identifier (e.g., "S001", "S002")
     * @param value Configuration value
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error configureSystemSettings(const std::string& commandKey, uint64_t value);

    // ===== MASTER ASSIGN DATA API (Function Code: 0b111) =====
    
    /**
     * @brief Configure user settings (U-series commands)
     * @param commandKey Command identifier (e.g., "U001", "U002")
     * @param value Configuration value
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error configureUserSettings(const std::string& commandKey, uint64_t value);

    /**
     * @brief AI model data operation (W-series commands)
     * @param slaveAddress Target slave device address
     * @param address Memory address in FRAM
     * @param data Data buffer for read/write operations
     * @param isWrite true for write operation, false for read
     * @param length Number of bytes to read/write
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error modelDataOperation(uint8_t slaveAddress, uint32_t address, 
                                 uint8_t* data, bool isWrite, size_t length);

    // ===== MASTER REQUEST API (Function Code: 0b110) =====
    
    /**
     * @brief Request data from slave device (A-series commands)
     * @param slaveAddress Target slave device address
     * @param dataKey Data identifier (e.g., "A001", "A002")
     * @param responseData Output buffer for response data
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error requestData(uint8_t slaveAddress, const std::string& dataKey, 
                          std::vector<uint8_t>& responseData);

    // ===== SLAVE RESPONSE API (Function Codes: 0b010, 0b001) =====
    
    /**
     * @brief Receive slave response
     * @param slaveAddress Expected slave device address (0 for any)
     * @param responseData Output buffer for response data
     * @param waitForData true to wait for data, false to return immediately
     * @param timeout Timeout in milliseconds (0 for infinite)
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                   bool waitForData = true, uint32_t timeout = RS485_DEFAULT_TIMEOUT);

    /**
     * @brief Register data ready callback
     * @param callback Function to call when response data is available
     */
    void registerDataReadyCallback(DataReadyCallbackFn callback);

    // ===== UTILITY FUNCTIONS =====
    
    /**
     * @brief Initialize the driver
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error Initialize();

    /**
     * @brief Run main application loop
     * @return Exit code (0 for success)
     */
    int RunApplication();

    /**
     * @brief Set current slave address for U-series commands
     * @param address Slave address (1-31)
     */
    void setCurrentSlaveAddress(uint8_t address);

    /**
     * @brief Get current slave address
     * @return Current slave address
     */
    uint8_t getCurrentSlaveAddress() const;

private:
    // Forward declarations for implementation classes
    class Impl;
    std::unique_ptr<Impl> m_pImpl;

    // Prevent copying
    RS485Driver(const RS485Driver&) = delete;
    RS485Driver& operator=(const RS485Driver&) = delete;
};

#endif // RS485_DRIVER_H
