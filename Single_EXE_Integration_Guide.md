# Single EXE Integration Guide - FTDI Driver + RS485 Application

## 1. Overview

This guide explains how to integrate the FTDI driver (`CDM2123620_Setup.exe`) directly into your RS485 application, creating a single executable that users only need to install once.

## 2. Integration Approaches

### 2.1 Recommended Approach: Embedded Driver Installation

Instead of requiring separate FTDI driver installation, we'll embed the FTDI driver files directly into your application and install them programmatically.

```
Your_RS485_Application.exe
├── Application Logic (RS485 Protocol)
├── Embedded FTDI Driver Files
├── Driver Installation Logic
└── User Interface
```

### 2.2 Why This Approach Works

1. **Single Installation**: Users only run one installer
2. **Version Control**: Ensure compatible FTDI driver version
3. **Simplified Deployment**: No dependency on external installers
4. **Better User Experience**: One-click installation

## 3. Step-by-Step Implementation

### 3.1 Environment Setup

#### Required Tools:
```
1. Visual Studio 2019/2022 (Community/Professional)
2. Windows Driver Kit (WDK) 10
3. Windows SDK 10
4. NSIS (Nullsoft Scriptable Install System) or Advanced Installer
5. Resource Hacker (for embedding files)
```

#### Installation Commands:
```cmd
# Install Visual Studio 2022 Community
winget install Microsoft.VisualStudio.2022.Community

# Install Windows SDK
winget install Microsoft.WindowsSDK.10

# Install WDK (download from Microsoft)
# https://docs.microsoft.com/en-us/windows-hardware/drivers/download-the-wdk

# Install NSIS
winget install NSIS.NSIS
```

### 3.2 Extract FTDI Driver Files

#### Step 1: Extract CDM2123620_Setup.exe
```cmd
# Create working directory
mkdir D:\RS485_Integration
cd D:\RS485_Integration

# Extract FTDI installer (using 7-Zip or similar)
7z x "D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\CDM2123620_Setup.exe" -o"FTDI_Extracted"
```

#### Step 2: Identify Required Files
After extraction, you'll find these key files:
```
FTDI_Extracted/
├── ftdibus.sys          # FTDI USB driver
├── ftdiport.sys         # FTDI serial port driver
├── ftdibus.inf          # Driver installation info
├── ftdiport.inf         # Port driver installation info
├── ftd2xx.dll           # FTDI D2XX library
├── ftd2xx.lib           # Import library
└── ftd2xx.h             # Header file
```

### 3.3 Create Integrated Application Project

#### Project Structure:
```
RS485_Integrated_Application/
├── src/
│   ├── main.cpp                 # Main application
│   ├── RS485Driver.cpp          # Your RS485 implementation
│   ├── FTDIInstaller.cpp        # FTDI driver installation logic
│   └── S001_S002_Handler.cpp    # Command handlers
├── include/
│   ├── RS485Driver.h
│   ├── FTDIInstaller.h
│   └── ftd2xx.h                 # FTDI header
├── resources/
│   ├── drivers/
│   │   ├── ftdibus.sys
│   │   ├── ftdiport.sys
│   │   ├── ftdibus.inf
│   │   └── ftdiport.inf
│   ├── libs/
│   │   ├── ftd2xx.dll
│   │   └── ftd2xx.lib
│   └── application.rc           # Resource file
├── installer/
│   └── setup_script.nsi         # NSIS installer script
└── CMakeLists.txt               # Build configuration
```

### 3.4 Embed FTDI Files as Resources

#### Step 1: Create Resource File (application.rc)
```rc
#include "resource.h"

// Embed FTDI driver files
IDR_FTDIBUS_SYS     RCDATA  "resources\\drivers\\ftdibus.sys"
IDR_FTDIPORT_SYS    RCDATA  "resources\\drivers\\ftdiport.sys"
IDR_FTDIBUS_INF     RCDATA  "resources\\drivers\\ftdibus.inf"
IDR_FTDIPORT_INF    RCDATA  "resources\\drivers\\ftdiport.inf"
IDR_FTD2XX_DLL      RCDATA  "resources\\libs\\ftd2xx.dll"

// Application resources
IDI_MAIN_ICON       ICON    "resources\\app_icon.ico"
IDS_APP_TITLE       STRING  "RS485 Communication Application"
```

#### Step 2: Create Resource Header (resource.h)
```cpp
#ifndef RESOURCE_H
#define RESOURCE_H

#define IDR_FTDIBUS_SYS     101
#define IDR_FTDIPORT_SYS    102
#define IDR_FTDIBUS_INF     103
#define IDR_FTDIPORT_INF    104
#define IDR_FTD2XX_DLL      105
#define IDI_MAIN_ICON       106
#define IDS_APP_TITLE       107

#endif // RESOURCE_H
```

### 3.5 Implement FTDI Driver Installation Logic

#### FTDIInstaller.h
```cpp
#ifndef FTDI_INSTALLER_H
#define FTDI_INSTALLER_H

#include <windows.h>
#include <string>

class FTDIInstaller {
public:
    enum class InstallResult {
        SUCCESS,
        ALREADY_INSTALLED,
        EXTRACTION_FAILED,
        INSTALLATION_FAILED,
        INSUFFICIENT_PRIVILEGES
    };

    static InstallResult InstallFTDIDriver();
    static bool IsFTDIDriverInstalled();
    static bool ExtractEmbeddedFiles();
    static std::wstring GetTempDirectory();
    
private:
    static bool ExtractResource(int resourceId, const std::wstring& outputPath);
    static bool InstallDriverFromINF(const std::wstring& infPath);
    static bool CheckAdminPrivileges();
};

#endif // FTDI_INSTALLER_H
```

#### FTDIInstaller.cpp
```cpp
#include "FTDIInstaller.h"
#include "resource.h"
#include <setupapi.h>
#include <newdev.h>
#include <shlobj.h>
#include <iostream>

#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "newdev.lib")

FTDIInstaller::InstallResult FTDIInstaller::InstallFTDIDriver() {
    // Check if running as administrator
    if (!CheckAdminPrivileges()) {
        return InstallResult::INSUFFICIENT_PRIVILEGES;
    }
    
    // Check if FTDI driver is already installed
    if (IsFTDIDriverInstalled()) {
        return InstallResult::ALREADY_INSTALLED;
    }
    
    // Extract embedded driver files
    if (!ExtractEmbeddedFiles()) {
        return InstallResult::EXTRACTION_FAILED;
    }
    
    // Install the driver
    std::wstring tempDir = GetTempDirectory();
    std::wstring infPath = tempDir + L"\\ftdibus.inf";
    
    if (!InstallDriverFromINF(infPath)) {
        return InstallResult::INSTALLATION_FAILED;
    }
    
    return InstallResult::SUCCESS;
}

bool FTDIInstaller::ExtractEmbeddedFiles() {
    std::wstring tempDir = GetTempDirectory();
    
    // Create temporary directory
    CreateDirectoryW(tempDir.c_str(), NULL);
    
    // Extract all embedded files
    bool success = true;
    success &= ExtractResource(IDR_FTDIBUS_SYS, tempDir + L"\\ftdibus.sys");
    success &= ExtractResource(IDR_FTDIPORT_SYS, tempDir + L"\\ftdiport.sys");
    success &= ExtractResource(IDR_FTDIBUS_INF, tempDir + L"\\ftdibus.inf");
    success &= ExtractResource(IDR_FTDIPORT_INF, tempDir + L"\\ftdiport.inf");
    success &= ExtractResource(IDR_FTD2XX_DLL, tempDir + L"\\ftd2xx.dll");
    
    return success;
}

bool FTDIInstaller::ExtractResource(int resourceId, const std::wstring& outputPath) {
    HRSRC hResource = FindResource(NULL, MAKEINTRESOURCE(resourceId), RT_RCDATA);
    if (!hResource) return false;
    
    HGLOBAL hLoadedResource = LoadResource(NULL, hResource);
    if (!hLoadedResource) return false;
    
    LPVOID pLockedResource = LockResource(hLoadedResource);
    if (!pLockedResource) return false;
    
    DWORD dwResourceSize = SizeofResource(NULL, hResource);
    if (dwResourceSize == 0) return false;
    
    // Write to file
    HANDLE hFile = CreateFileW(outputPath.c_str(), GENERIC_WRITE, 0, NULL, 
                              CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) return false;
    
    DWORD dwBytesWritten;
    BOOL bResult = WriteFile(hFile, pLockedResource, dwResourceSize, &dwBytesWritten, NULL);
    CloseHandle(hFile);
    
    return bResult && (dwBytesWritten == dwResourceSize);
}

bool FTDIInstaller::InstallDriverFromINF(const std::wstring& infPath) {
    // Use Windows API to install driver
    BOOL rebootRequired = FALSE;
    
    BOOL result = UpdateDriverForPlugAndPlayDevicesW(
        NULL,                           // Parent window
        L"USB\\VID_0403&PID_6001",     // Hardware ID for FTDI device
        infPath.c_str(),               // INF file path
        INSTALLFLAG_FORCE,             // Installation flags
        &rebootRequired                // Reboot required flag
    );
    
    if (rebootRequired) {
        MessageBoxW(NULL, L"System reboot required to complete driver installation.", 
                   L"Driver Installation", MB_OK | MB_ICONINFORMATION);
    }
    
    return result != FALSE;
}

std::wstring FTDIInstaller::GetTempDirectory() {
    wchar_t tempPath[MAX_PATH];
    GetTempPathW(MAX_PATH, tempPath);
    return std::wstring(tempPath) + L"RS485_FTDI_Temp";
}

bool FTDIInstaller::CheckAdminPrivileges() {
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    return isAdmin != FALSE;
}

bool FTDIInstaller::IsFTDIDriverInstalled() {
    // Check if FTDI device is present and has driver
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(NULL, L"USB", NULL, 
                                                 DIGCF_PRESENT | DIGCF_ALLCLASSES);
    if (deviceInfoSet == INVALID_HANDLE_VALUE) return false;
    
    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        wchar_t hardwareId[256];
        if (SetupDiGetDeviceRegistryPropertyW(deviceInfoSet, &deviceInfoData, 
                                            SPDRP_HARDWAREID, NULL, 
                                            (PBYTE)hardwareId, sizeof(hardwareId), NULL)) {
            if (wcsstr(hardwareId, L"VID_0403&PID_6001")) {
                SetupDiDestroyDeviceInfoList(deviceInfoSet);
                return true;
            }
        }
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    return false;
}
```

### 3.6 Main Application Integration

#### main.cpp
```cpp
#include <windows.h>
#include <iostream>
#include "FTDIInstaller.h"
#include "RS485Driver.h"

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Check and install FTDI driver if needed
    std::wcout << L"Checking FTDI driver installation..." << std::endl;

    auto installResult = FTDIInstaller::InstallFTDIDriver();

    switch (installResult) {
        case FTDIInstaller::InstallResult::SUCCESS:
            std::wcout << L"FTDI driver installed successfully!" << std::endl;
            break;

        case FTDIInstaller::InstallResult::ALREADY_INSTALLED:
            std::wcout << L"FTDI driver already installed." << std::endl;
            break;

        case FTDIInstaller::InstallResult::INSUFFICIENT_PRIVILEGES:
            MessageBoxW(NULL, L"Please run as Administrator to install drivers.",
                       L"Administrator Required", MB_OK | MB_ICONERROR);
            return 1;

        case FTDIInstaller::InstallResult::INSTALLATION_FAILED:
            MessageBoxW(NULL, L"Failed to install FTDI driver.",
                       L"Installation Error", MB_OK | MB_ICONERROR);
            return 1;

        default:
            MessageBoxW(NULL, L"Unknown error during driver installation.",
                       L"Error", MB_OK | MB_ICONERROR);
            return 1;
    }

    // Initialize RS485 communication
    RS485Driver driver;
    if (driver.Initialize() != RS485Error::SUCCESS) {
        MessageBoxW(NULL, L"Failed to initialize RS485 communication.",
                   L"Initialization Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Start main application
    std::wcout << L"RS485 Communication Application started successfully!" << std::endl;

    // Your main application logic here
    return driver.RunApplication();
}
```

## 4. Build Configuration

### 4.1 CMakeLists.txt
```cmake
cmake_minimum_required(VERSION 3.16)
project(RS485_Integrated_Application)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)

# Include directories
include_directories(include)
include_directories(resources)

# Source files
set(SOURCES
    src/main.cpp
    src/RS485Driver.cpp
    src/FTDIInstaller.cpp
    src/S001_S002_Handler.cpp
)

# Resource files
set(RESOURCES
    resources/application.rc
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${RESOURCES})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    setupapi
    newdev
    advapi32
    user32
    kernel32
)

# Set executable properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "RS485_Communication_Application"
)

# Copy FTDI DLL to output directory
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_SOURCE_DIR}/resources/libs/ftd2xx.dll"
    $<TARGET_FILE_DIR:${PROJECT_NAME}>
)
```

### 4.2 Build Commands
```cmd
# Create build directory
mkdir build
cd build

# Configure with CMake
cmake .. -G "Visual Studio 17 2022" -A x64

# Build the project
cmake --build . --config Release

# The output will be in build/Release/RS485_Communication_Application.exe
```

## 5. Advanced Packaging with NSIS

### 5.1 NSIS Installer Script (setup_script.nsi)
```nsis
!define APP_NAME "RS485 Communication Application"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Your Company"
!define APP_EXE "RS485_Communication_Application.exe"

Name "${APP_NAME}"
OutFile "RS485_Setup.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Main Application" SecMain
    SetOutPath "$INSTDIR"

    # Copy main application
    File "build\Release\${APP_EXE}"
    File "build\Release\ftd2xx.dll"

    # Create shortcuts
    CreateDirectory "$SMPROGRAMS\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"

    # Register uninstaller
    WriteUninstaller "$INSTDIR\Uninstall.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                     "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" \
                     "UninstallString" "$INSTDIR\Uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\${APP_EXE}"
    Delete "$INSTDIR\ftd2xx.dll"
    Delete "$INSTDIR\Uninstall.exe"

    Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
    Delete "$DESKTOP\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\${APP_NAME}"
    RMDir "$INSTDIR"

    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
SectionEnd
```

### 5.2 Create Installer
```cmd
# Compile NSIS script
makensis setup_script.nsi

# This creates RS485_Setup.exe - your final installer
```

## 6. Testing and Validation

### 6.1 Test Checklist
- [ ] Application runs without FTDI driver pre-installed
- [ ] Driver installation works on clean Windows system
- [ ] S001 and S002 commands function correctly
- [ ] No conflicts with existing FTDI installations
- [ ] Uninstaller removes all components cleanly

### 6.2 Test Commands
```cmd
# Test on clean VM
# 1. Install your RS485_Setup.exe
# 2. Verify FTDI driver in Device Manager
# 3. Test RS485 communication
# 4. Uninstall and verify cleanup
```

## 7. Deployment Strategy

### 7.1 Final Package Structure
```
RS485_Communication_Package/
├── RS485_Setup.exe              # Single installer for users
├── Documentation/
│   ├── User_Manual.pdf
│   └── Installation_Guide.pdf
└── Source_Code/                 # For developers
    ├── src/
    ├── include/
    └── build_instructions.md
```

### 7.2 Distribution
1. **Single File Distribution**: Users only need `RS485_Setup.exe`
2. **No Dependencies**: Everything embedded in the installer
3. **Administrator Rights**: Required only during installation
4. **Clean Uninstall**: Complete removal when needed

## 8. 具体操作步骤 (Detailed Operation Steps)

### 8.1 环境准备 (Environment Setup)

#### 第一步：安装开发工具
```cmd
# 1. 下载并安装 Visual Studio 2022 Community (免费)
# 从 https://visualstudio.microsoft.com/downloads/ 下载

# 2. 安装时选择以下组件：
# - Desktop development with C++
# - Windows 10/11 SDK (latest version)
# - CMake tools for C++

# 3. 下载并安装 Windows Driver Kit (WDK)
# 从 https://docs.microsoft.com/en-us/windows-hardware/drivers/download-the-wdk

# 4. 安装 NSIS (用于创建安装包)
# 从 https://nsis.sourceforge.io/Download 下载
```

#### 第二步：创建项目目录
```cmd
# 在您的开发目录创建项目
cd D:\wjw_new_file\Software_design\RS485_Driver\
mkdir RS485_Integrated_App
cd RS485_Integrated_App

# 创建项目结构
mkdir src include resources installer build
mkdir resources\drivers resources\libs
```

### 8.2 提取FTDI驱动文件

#### 第三步：解压FTDI安装包
```cmd
# 使用7-Zip或WinRAR解压CDM2123620_Setup.exe
# 如果没有7-Zip，可以下载：https://www.7-zip.org/

# 解压命令（假设已安装7-Zip）
"C:\Program Files\7-Zip\7z.exe" x "D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\CDM2123620_Setup.exe" -o"FTDI_Extracted"

# 或者手动操作：
# 1. 右键点击 CDM2123620_Setup.exe
# 2. 选择 "7-Zip" -> "Extract to FTDI_Extracted\"
```

#### 第四步：复制必要文件
```cmd
# 从解压的文件中复制以下文件到您的项目：

# 复制驱动文件到 resources\drivers\
copy "FTDI_Extracted\ftdibus.sys" "resources\drivers\"
copy "FTDI_Extracted\ftdiport.sys" "resources\drivers\"
copy "FTDI_Extracted\ftdibus.inf" "resources\drivers\"
copy "FTDI_Extracted\ftdiport.inf" "resources\drivers\"

# 复制库文件到 resources\libs\
copy "FTDI_Extracted\amd64\ftd2xx.dll" "resources\libs\"
copy "FTDI_Extracted\amd64\ftd2xx.lib" "resources\libs\"
copy "FTDI_Extracted\ftd2xx.h" "include\"
```

### 8.3 创建项目文件

#### 第五步：创建源代码文件
按照前面提供的代码模板创建以下文件：

1. **include\resource.h** - 资源定义
2. **resources\application.rc** - 资源文件
3. **include\FTDIInstaller.h** - FTDI安装器头文件
4. **src\FTDIInstaller.cpp** - FTDI安装器实现
5. **src\main.cpp** - 主程序
6. **CMakeLists.txt** - 构建配置

#### 第六步：配置构建系统
```cmd
# 在项目根目录创建 CMakeLists.txt
# 使用前面提供的CMake配置

# 创建构建目录并配置
mkdir build
cd build

# 配置项目（64位）
cmake .. -G "Visual Studio 17 2022" -A x64

# 如果使用Visual Studio 2019：
# cmake .. -G "Visual Studio 16 2019" -A x64
```

### 8.4 编译和构建

#### 第七步：编译项目
```cmd
# 在build目录中执行
cmake --build . --config Release

# 或者打开生成的.sln文件在Visual Studio中编译
start RS485_Integrated_Application.sln
```

#### 第八步：测试可执行文件
```cmd
# 测试生成的exe文件
cd Release
.\RS485_Communication_Application.exe

# 注意：首次运行需要管理员权限来安装FTDI驱动
```

### 8.5 创建安装包

#### 第九步：准备NSIS脚本
```cmd
# 在installer目录创建setup_script.nsi
# 使用前面提供的NSIS脚本模板

# 修改脚本中的路径指向您的build\Release目录
```

#### 第十步：生成最终安装包
```cmd
# 使用NSIS编译安装脚本
cd installer
"C:\Program Files (x86)\NSIS\makensis.exe" setup_script.nsi

# 这将生成 RS485_Setup.exe - 您的最终安装包
```

## 9. 验证和测试

### 9.1 完整测试流程

#### 测试步骤：
```cmd
# 1. 在干净的Windows系统（或虚拟机）上测试
# 2. 运行 RS485_Setup.exe（需要管理员权限）
# 3. 验证安装：
#    - 检查程序是否出现在开始菜单
#    - 检查设备管理器中是否有FTDI设备
#    - 运行程序测试S001和S002命令

# 4. 测试卸载：
#    - 通过控制面板卸载程序
#    - 验证所有文件和注册表项都被清理
```

### 9.2 常见问题解决

#### 问题1：编译错误
```cmd
# 确保安装了正确的Windows SDK版本
# 检查CMakeLists.txt中的路径是否正确
# 验证所有头文件和库文件都在正确位置
```

#### 问题2：驱动安装失败
```cmd
# 确保以管理员身份运行
# 检查Windows版本兼容性
# 验证驱动文件完整性
```

#### 问题3：NSIS编译失败
```cmd
# 检查文件路径是否正确
# 确保所有引用的文件都存在
# 验证NSIS语法
```

## 10. 最终交付

### 10.1 交付物清单
- **RS485_Setup.exe** - 用户安装包（单一文件）
- **用户手册** - 安装和使用说明
- **源代码** - 完整的项目源码
- **构建说明** - 开发者构建指南

### 10.2 用户使用流程
1. 用户下载 `RS485_Setup.exe`
2. 右键选择"以管理员身份运行"
3. 按照安装向导完成安装
4. 启动程序开始使用RS485通信功能

这样，用户只需要安装一次就能获得完整的RS485通信功能，包括FTDI驱动和您的应用程序！
