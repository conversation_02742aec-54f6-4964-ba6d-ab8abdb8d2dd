@echo off
echo ========================================
echo   RS485 UMDF Driver Installation
echo ========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Administrator privileges required!
    echo.
    echo Please run this script as Administrator:
    echo 1. Right-click this batch file
    echo 2. Select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Running with administrator privileges ✓
echo.

REM Check if driver files exist
if not exist "build\driver\RS485Filter.dll" (
    echo ERROR: Driver file not found!
    echo Please build the driver first using build_umdf.bat
    echo.
    pause
    exit /b 1
)

if not exist "driver\RS485Filter.inf" (
    echo ERROR: Driver INF file not found!
    echo Please ensure driver\RS485Filter.inf exists.
    echo.
    pause
    exit /b 1
)

echo Driver files found ✓
echo.

echo ========================================
echo Step 1: Enable Test Signing
echo ========================================
echo.

echo Enabling test signing mode...
bcdedit /set testsigning on
if %ERRORLEVEL% EQU 0 (
    echo ✓ Test signing enabled successfully
    echo Note: A reboot may be required for this to take effect
) else (
    echo ⚠ Warning: Failed to enable test signing
    echo This may be required for unsigned drivers
)
echo.

echo ========================================
echo Step 2: Install FTDI Base Driver
echo ========================================
echo.

REM Check if FTDI driver is already installed
echo Checking for existing FTDI driver...
pnputil /enum-drivers | findstr /i "ftdi" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ FTDI driver already installed
) else (
    echo Installing FTDI driver...
    REM This would install the FTDI driver if available
    echo Note: Please install CDM2123620_Setup.exe manually if not already done
)
echo.

echo ========================================
echo Step 3: Install RS485 Filter Driver
echo ========================================
echo.

echo Installing RS485 filter driver...
pnputil /add-driver driver\RS485Filter.inf /install
if %ERRORLEVEL% EQU 0 (
    echo ✓ RS485 filter driver installed successfully
) else (
    echo ✗ Failed to install RS485 filter driver
    echo.
    echo Troubleshooting:
    echo 1. Ensure the driver is properly signed
    echo 2. Check that test signing is enabled
    echo 3. Verify INF file syntax
    echo.
    pause
    exit /b 1
)
echo.

echo ========================================
echo Step 4: Copy Driver Files
echo ========================================
echo.

echo Copying driver DLL to system directory...
copy "build\driver\RS485Filter.dll" "%SystemRoot%\System32\drivers\UMDF\" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Driver DLL copied successfully
) else (
    echo Creating UMDF directory and copying...
    mkdir "%SystemRoot%\System32\drivers\UMDF\" >nul 2>&1
    copy "build\driver\RS485Filter.dll" "%SystemRoot%\System32\drivers\UMDF\"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Driver DLL copied successfully
    ) else (
        echo ⚠ Warning: Failed to copy driver DLL
    )
)
echo.

echo ========================================
echo Step 5: Verify Installation
echo ========================================
echo.

echo Checking installed drivers...
pnputil /enum-drivers | findstr /i "RS485"
if %ERRORLEVEL% EQU 0 (
    echo ✓ RS485 driver found in system
) else (
    echo ⚠ RS485 driver not found in driver list
)
echo.

echo Checking device manager...
devcon status "USB\VID_0403&PID_6001" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ FTDI device found
) else (
    echo ⚠ FTDI device not found (may not be connected)
)
echo.

echo ========================================
echo Installation Complete
echo ========================================
echo.
echo The RS485 UMDF driver has been installed.
echo.
echo Next steps:
echo 1. Connect your USB-RS485-WE-1800-BT device
echo 2. Verify the device appears in Device Manager
echo 3. Run RS485_UMDF_Application.exe to test communication
echo.
echo If you enabled test signing, you may need to reboot.
echo.

set /p REBOOT="Do you want to reboot now? (y/n): "
if /i "%REBOOT%"=="y" (
    echo Rebooting in 10 seconds...
    shutdown /r /t 10 /c "Reboot required for driver installation"
) else (
    echo Please reboot manually when convenient.
)

echo.
echo Press any key to exit...
pause >nul
