#include "resource.h"
#include <windows.h>

// Application Icon
IDI_MAIN_ICON ICON "app_icon.ico"

// Version Information
IDR_VERSION_INFO VERSIONINFO
FILEVERSION 1,0,0,0
PRODUCTVERSION 1,0,0,0
FIL<PERSON><PERSON>GSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS 0x0L
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_APP
FILESUBTYPE VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Your Company"
            VALUE "FileDescription", "RS485 Communication Application"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "RS485_Communication_Application"
            VALUE "LegalCopyright", "Copyright (C) 2024"
            VALUE "OriginalFilename", "RS485_Communication_Application.exe"
            VALUE "ProductName", "RS485 Driver Suite"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

// Application Title String
STRINGTABLE
BEGIN
    IDS_APP_TITLE "RS485 Communication Application"
END

// Embedded FTDI Driver Files
// Note: These resources will be populated during the build process
// The actual driver files need to be extracted from CDM2123620_Setup.exe first

// IDR_FTDIBUS_SYS     RCDATA  "drivers\\ftdibus.sys"
// IDR_FTDIPORT_SYS    RCDATA  "drivers\\ftdiport.sys"
// IDR_FTDIBUS_INF     RCDATA  "drivers\\ftdibus.inf"
// IDR_FTDIPORT_INF    RCDATA  "drivers\\ftdiport.inf"
// IDR_FTD2XX_DLL      RCDATA  "libs\\ftd2xx.dll"

// Manifest for Windows compatibility and admin privileges
1 RT_MANIFEST "application.manifest"
