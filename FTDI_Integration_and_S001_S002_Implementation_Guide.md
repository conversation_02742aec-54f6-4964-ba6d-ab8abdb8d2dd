# FTDI Driver Integration and S001/S002 Command Implementation Guide

## 1. Overview

This document provides a comprehensive guide for integrating the FTDI driver (`CDM2123620_Setup.exe`) with Windows User-Mode Driver Framework (UMDF 2) and implementing the first two RS485 commands: S001 (Set RS485 slave address) and S002 (Set baud rate).

### 1.1 Project Goals

- Integrate FTDI VCP driver functionality into UMDF 2 framework
- Implement S001 command for slave address assignment
- Implement S002 command for baud rate configuration
- Establish communication between PC and FPGA via RS485

### 1.2 Hardware Setup

- **PC Side**: USB-RS485-WE-1800-BT FTDI converter
- **FPGA Side**: ZM-AISL-01 board with RS485 interface
- **Connection**: Single twisted pair RS485 cable
- **Protocol**: ZES protocol with 16-byte frame format

## 2. FTDI Driver Integration Strategy

### 2.1 FTDI Driver Analysis

The `CDM2123620_Setup.exe` contains:
- FTDI Virtual COM Port (VCP) drivers
- D2XX direct driver interface
- USB device descriptors and configuration

### 2.2 Integration Approach

Instead of replacing the FTDI driver, we will create a **filter driver** that sits above the FTDI VCP driver:

```
┌─────────────────────────────────────┐
│     Your UMDF 2 Application        │
│   (RS485 Protocol Handler)         │
├─────────────────────────────────────┤
│    UMDF 2 Filter Driver             │
│  (ZES Protocol Implementation)      │
├─────────────────────────────────────┤
│    FTDI VCP Driver                  │
│  (USB-to-Serial Conversion)        │
├─────────────────────────────────────┤
│    Windows USB Stack               │
├─────────────────────────────────────┤
│  USB-RS485-WE-1800-BT Hardware     │
└─────────────────────────────────────┘
```

### 2.3 Why Filter Driver Approach

1. **Preserve FTDI Functionality**: Keep all FTDI optimizations and hardware support
2. **Add Protocol Intelligence**: Implement ZES protocol on top of serial communication
3. **Simplified Development**: Focus on protocol implementation, not USB details
4. **Better Compatibility**: Works with existing FTDI infrastructure

## 3. UMDF 2 Filter Driver Implementation

### 3.1 Driver Architecture

```cpp
// Main driver class
class RS485FilterDriver : public CUnknown
{
private:
    IWDFDevice*             m_pWdfDevice;
    IWDFIoQueue*            m_pDefaultQueue;
    IWDFFile*               m_pSerialPort;
    
    // ZES Protocol components
    FrameProcessor          m_frameProcessor;
    BufferManager           m_bufferManager;
    
public:
    // IUnknown methods
    STDMETHOD(QueryInterface)(REFIID riid, void** ppvObject);
    STDMETHOD_(ULONG, AddRef)();
    STDMETHOD_(ULONG, Release)();
    
    // Driver lifecycle
    HRESULT Initialize(IWDFDevice* pWdfDevice);
    HRESULT Configure();
    void Cleanup();
    
    // I/O handling
    HRESULT OnDeviceAdd(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit);
    void OnIoDefault(IWDFIoQueue* pQueue, IWDFIoRequest* pRequest);
};
```

### 3.2 Device Installation and Setup

1. **Install FTDI Driver First**:
   ```cmd
   # Run as Administrator
   CDM2123620_Setup.exe
   ```

2. **Create Filter Driver INF File**:
   ```ini
   [Version]
   Signature="$WINDOWS NT$"
   Class=Ports
   ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
   Provider=%ManufacturerName%
   CatalogFile=RS485Filter.cat
   DriverVer=01/01/2024,1.0.0.0
   
   [Manufacturer]
   %ManufacturerName%=Standard,NT$ARCH$
   
   [Standard.NT$ARCH$]
   %RS485Filter.DeviceDesc%=RS485Filter_Install,USB\VID_0403&PID_6001
   
   [RS485Filter_Install]
   Include=umdf.inf
   Needs=UMDF_Install
   CopyFiles=UMDriverCopy
   
   [RS485Filter_Install.Services]
   AddService=,0x00000002   ; null service install
   
   [RS485Filter_Install.Wdf]
   UmdfService=RS485FilterDriver,RS485Filter_Install
   UmdfServiceOrder=RS485FilterDriver
   UmdfDirectHardwareAccess=AllowDirectHardwareAccess
   ```

## 4. S001 Command Implementation (Set RS485 Slave Address)

### 4.1 Command Specification

| Parameter | Value |
|-----------|-------|
| **Command Key** | "S001" |
| **Function Code** | 0b111 (Assign data) |
| **Target Address** | 0x00 (Broadcast) |
| **Value Range** | 1-31 |
| **Frame Format** | 16 bytes total |

### 4.2 PC Side Implementation

```cpp
class S001CommandHandler
{
public:
    RS485Error ExecuteS001Command(uint8_t newSlaveAddress)
    {
        // Validate input
        if (newSlaveAddress < 1 || newSlaveAddress > 31) {
            return RS485Error::INVALID_PARAMETER;
        }
        
        // Check only one slave is connected
        if (!VerifySingleSlaveConnection()) {
            return RS485Error::MULTIPLE_SLAVES_DETECTED;
        }
        
        // Build S001 frame
        RS485Frame frame;
        BuildS001Frame(frame, newSlaveAddress);
        
        // Send frame
        RS485Error result = SendFrame(frame);
        if (result != RS485Error::SUCCESS) {
            return result;
        }
        
        // Wait for acknowledgment
        return WaitForAcknowledgment(1000); // 1 second timeout
    }
    
private:
    void BuildS001Frame(RS485Frame& frame, uint8_t slaveAddress)
    {
        frame.header = 0xAA;
        frame.id_byte = 0xE0;  // Function code 0b111 + address 0x00
        
        // Payload: "S001" + slave address value
        memcpy(frame.payload, "S001", 4);
        memset(frame.payload + 4, 0, 4);  // Clear upper 4 bytes
        frame.payload[4] = slaveAddress;  // Set new address
        memset(frame.payload + 5, 0, 7); // Clear remaining bytes
        
        frame.crc8 = CalculateCRC8(frame.id_byte, frame.payload, 12);
        frame.trailer = 0x0D;
    }
};
```

### 4.3 FPGA Side Implementation (VHDL/Verilog)

```vhdl
-- S001 Command Handler Entity
entity S001_Handler is
    Port (
        clk         : in  STD_LOGIC;
        reset       : in  STD_LOGIC;
        
        -- Frame input
        frame_valid : in  STD_LOGIC;
        frame_data  : in  STD_LOGIC_VECTOR(127 downto 0); -- 16 bytes
        
        -- Current slave address
        current_addr: in  STD_LOGIC_VECTOR(4 downto 0);
        
        -- FRAM interface
        fram_write  : out STD_LOGIC;
        fram_addr   : out STD_LOGIC_VECTOR(15 downto 0);
        fram_data   : out STD_LOGIC_VECTOR(7 downto 0);
        
        -- Response interface
        ack_valid   : out STD_LOGIC;
        ack_data    : out STD_LOGIC_VECTOR(127 downto 0);
        
        -- Status
        addr_updated: out STD_LOGIC
    );
end S001_Handler;

architecture Behavioral of S001_Handler is
    signal new_address : STD_LOGIC_VECTOR(4 downto 0);
    signal command_key : STD_LOGIC_VECTOR(31 downto 0);
    
begin
    process(clk, reset)
    begin
        if reset = '1' then
            addr_updated <= '0';
            ack_valid <= '0';
            fram_write <= '0';
            
        elsif rising_edge(clk) then
            if frame_valid = '1' then
                -- Extract command key (first 4 bytes of payload)
                command_key <= frame_data(95 downto 64);
                
                -- Check if this is S001 command
                if command_key = x"53303031" then -- "S001" in ASCII
                    -- Extract new address from payload
                    new_address <= frame_data(63 downto 59);
                    
                    -- Validate address range (1-31)
                    if new_address >= "00001" and new_address <= "11111" then
                        -- Write to FRAM
                        fram_addr <= x"0000";  -- Address storage location
                        fram_data <= "000" & new_address;
                        fram_write <= '1';
                        
                        -- Generate acknowledgment
                        GenerateS001Acknowledgment(new_address);
                        
                        addr_updated <= '1';
                    end if;
                end if;
            else
                fram_write <= '0';
                ack_valid <= '0';
                addr_updated <= '0';
            end if;
        end if;
    end process;
    
    -- Acknowledgment generation procedure
    procedure GenerateS001Acknowledgment(addr : STD_LOGIC_VECTOR(4 downto 0)) is
    begin
        -- Build acknowledgment frame
        ack_data(127 downto 120) <= x"AA";           -- Header
        ack_data(119 downto 112) <= "010" & addr;    -- Function code 0b010 + new address
        ack_data(111 downto 80)  <= x"53303031";     -- "S001" echo
        ack_data(79 downto 72)   <= "000" & addr;    -- Confirm new address
        ack_data(71 downto 8)    <= (others => '0'); -- Padding
        ack_data(7 downto 0)     <= x"0D";           -- Trailer
        -- CRC calculation would be done in separate module
        
        ack_valid <= '1';
    end procedure;
    
end Behavioral;
```

## 5. S002 Command Implementation (Set Baud Rate)

### 5.1 Command Specification

| Parameter | Value |
|-----------|-------|
| **Command Key** | "S002" |
| **Function Code** | 0b111 (Assign data) |
| **Target Address** | Current slave address |
| **Valid Rates** | 9600, 19200, 38400, 57600, 115200 |

### 5.2 PC Side Implementation

```cpp
class S002CommandHandler
{
public:
    RS485Error ExecuteS002Command(uint8_t slaveAddress, uint32_t baudRate)
    {
        // Validate baud rate
        if (!IsValidBaudRate(baudRate)) {
            return RS485Error::INVALID_BAUD_RATE;
        }
        
        // Build S002 frame
        RS485Frame frame;
        BuildS002Frame(frame, slaveAddress, baudRate);
        
        // Send at current baud rate
        RS485Error result = SendFrame(frame);
        if (result != RS485Error::SUCCESS) {
            return result;
        }
        
        // Switch to new baud rate
        SetPortBaudRate(baudRate);
        
        // Wait for acknowledgment at new baud rate
        return WaitForAcknowledgment(2000); // 2 second timeout
    }
    
private:
    bool IsValidBaudRate(uint32_t rate)
    {
        return (rate == 9600 || rate == 19200 || rate == 38400 || 
                rate == 57600 || rate == 115200);
    }
    
    void BuildS002Frame(RS485Frame& frame, uint8_t addr, uint32_t rate)
    {
        frame.header = 0xAA;
        frame.id_byte = 0xE0 | addr;  // Function code 0b111 + slave address
        
        // Payload: "S002" + baud rate value
        memcpy(frame.payload, "S002", 4);
        *reinterpret_cast<uint32_t*>(frame.payload + 4) = rate;
        memset(frame.payload + 8, 0, 4); // Clear remaining bytes
        
        frame.crc8 = CalculateCRC8(frame.id_byte, frame.payload, 12);
        frame.trailer = 0x0D;
    }
};
```

### 5.3 FPGA Side Implementation (VHDL/Verilog)

```vhdl
-- S002 Command Handler Entity
entity S002_Handler is
    Port (
        clk         : in  STD_LOGIC;
        reset       : in  STD_LOGIC;

        -- Frame input
        frame_valid : in  STD_LOGIC;
        frame_data  : in  STD_LOGIC_VECTOR(127 downto 0);

        -- UART control
        uart_baud_set : out STD_LOGIC;
        uart_baud_rate: out STD_LOGIC_VECTOR(31 downto 0);

        -- FRAM interface
        fram_write  : out STD_LOGIC;
        fram_addr   : out STD_LOGIC_VECTOR(15 downto 0);
        fram_data   : out STD_LOGIC_VECTOR(31 downto 0);

        -- Response interface
        ack_valid   : out STD_LOGIC;
        ack_data    : out STD_LOGIC_VECTOR(127 downto 0);

        -- Status
        baud_updated: out STD_LOGIC
    );
end S002_Handler;

architecture Behavioral of S002_Handler is
    signal new_baud_rate : STD_LOGIC_VECTOR(31 downto 0);
    signal command_key   : STD_LOGIC_VECTOR(31 downto 0);

begin
    process(clk, reset)
    begin
        if reset = '1' then
            baud_updated <= '0';
            ack_valid <= '0';
            fram_write <= '0';
            uart_baud_set <= '0';

        elsif rising_edge(clk) then
            if frame_valid = '1' then
                -- Extract command key
                command_key <= frame_data(95 downto 64);

                -- Check if this is S002 command
                if command_key = x"53303032" then -- "S002" in ASCII
                    -- Extract baud rate from payload
                    new_baud_rate <= frame_data(63 downto 32);

                    -- Validate baud rate
                    if IsValidBaudRate(new_baud_rate) then
                        -- Store in FRAM
                        fram_addr <= x"0004";  -- Baud rate storage location
                        fram_data <= new_baud_rate;
                        fram_write <= '1';

                        -- Update UART baud rate
                        uart_baud_rate <= new_baud_rate;
                        uart_baud_set <= '1';

                        -- Generate acknowledgment at NEW baud rate
                        GenerateS002Acknowledgment(new_baud_rate);

                        baud_updated <= '1';
                    end if;
                end if;
            else
                fram_write <= '0';
                ack_valid <= '0';
                baud_updated <= '0';
                uart_baud_set <= '0';
            end if;
        end if;
    end process;

    -- Baud rate validation function
    function IsValidBaudRate(rate : STD_LOGIC_VECTOR(31 downto 0)) return boolean is
    begin
        case rate is
            when x"00002580" => return true; -- 9600
            when x"00004B00" => return true; -- 19200
            when x"00009600" => return true; -- 38400
            when x"0000E100" => return true; -- 57600
            when x"0001C200" => return true; -- 115200
            when others      => return false;
        end case;
    end function;

end Behavioral;
```

## 6. Development Steps and Timeline

### 6.1 Phase 1: Environment Setup (Week 1)

1. **Install FTDI Driver**:
   ```cmd
   # Run CDM2123620_Setup.exe as Administrator
   # Verify installation in Device Manager
   ```

2. **Setup Development Environment**:
   - Visual Studio 2019/2022 with WDK
   - Windows Driver Kit (WDK) 10
   - UMDF 2.0 templates and samples

3. **Hardware Verification**:
   - Connect USB-RS485 converter
   - Verify COM port enumeration
   - Test basic serial communication

### 6.2 Phase 2: Filter Driver Development (Week 2-3)

1. **Create UMDF 2 Filter Driver Project**
2. **Implement Basic Frame Processing**
3. **Add ZES Protocol Support**
4. **Integrate with FTDI VCP Driver**

### 6.3 Phase 3: S001 Implementation (Week 4)

1. **PC Side Development**:
   - Implement S001 command builder
   - Add broadcast frame handling
   - Implement acknowledgment waiting

2. **FPGA Side Development**:
   - Create S001 command parser
   - Implement FRAM address storage
   - Add acknowledgment generation

3. **Integration Testing**:
   - Test single slave address assignment
   - Verify FRAM persistence
   - Test error handling

### 6.4 Phase 4: S002 Implementation (Week 5)

1. **PC Side Development**:
   - Implement S002 command builder
   - Add baud rate switching logic
   - Handle timing synchronization

2. **FPGA Side Development**:
   - Create S002 command parser
   - Implement UART reconfiguration
   - Add baud rate validation

3. **Integration Testing**:
   - Test all supported baud rates
   - Verify synchronization
   - Test error recovery

## 7. Testing Strategy

### 7.1 Unit Testing

```cpp
// Test S001 command
TEST(S001Command, ValidAddressRange) {
    S001CommandHandler handler;

    // Test valid addresses
    EXPECT_EQ(RS485Error::SUCCESS, handler.ExecuteS001Command(1));
    EXPECT_EQ(RS485Error::SUCCESS, handler.ExecuteS001Command(31));

    // Test invalid addresses
    EXPECT_EQ(RS485Error::INVALID_PARAMETER, handler.ExecuteS001Command(0));
    EXPECT_EQ(RS485Error::INVALID_PARAMETER, handler.ExecuteS001Command(32));
}

// Test S002 command
TEST(S002Command, ValidBaudRates) {
    S002CommandHandler handler;

    // Test valid baud rates
    EXPECT_EQ(RS485Error::SUCCESS, handler.ExecuteS002Command(1, 9600));
    EXPECT_EQ(RS485Error::SUCCESS, handler.ExecuteS002Command(1, 115200));

    // Test invalid baud rate
    EXPECT_EQ(RS485Error::INVALID_BAUD_RATE, handler.ExecuteS002Command(1, 12345));
}
```

### 7.2 Integration Testing

1. **Hardware-in-the-Loop Testing**:
   - Real FPGA board connected via RS485
   - Automated test sequences
   - Error injection testing

2. **Performance Testing**:
   - Frame transmission timing
   - Acknowledgment response time
   - Baud rate switching speed

### 7.3 Compliance Testing

1. **ZES Protocol Compliance**:
   - Frame format verification
   - CRC calculation accuracy
   - Timing requirements

2. **Windows Driver Compliance**:
   - WHQL testing preparation
   - Power management testing
   - Plug and Play testing

## 8. Troubleshooting Guide

### 8.1 Common Issues

1. **FTDI Driver Not Detected**:
   - Verify USB connection
   - Check Device Manager
   - Reinstall FTDI driver

2. **S001 Command Fails**:
   - Ensure only one slave connected
   - Check frame format
   - Verify CRC calculation

3. **S002 Baud Rate Issues**:
   - Check timing synchronization
   - Verify UART configuration
   - Test with oscilloscope

### 8.2 Debug Tools

1. **Serial Port Monitor**: Monitor raw RS485 traffic
2. **Logic Analyzer**: Verify signal timing
3. **FPGA Debug**: Use ChipScope/SignalTap for internal signals

## 9. Key Implementation Files

### 9.1 Required PC Side Files

```
RS485_Driver/
├── src/
│   ├── RS485FilterDriver.cpp      # Main driver implementation
│   ├── S001CommandHandler.cpp     # S001 command implementation
│   ├── S002CommandHandler.cpp     # S002 command implementation
│   ├── FrameProcessor.cpp         # ZES frame processing
│   ├── BufferManager.cpp          # Buffer management
│   └── CRC8Calculator.cpp         # CRC calculation
├── include/
│   ├── RS485FilterDriver.h
│   ├── CommandHandlers.h
│   ├── ZESProtocol.h
│   └── RS485Errors.h
├── inf/
│   └── RS485Filter.inf            # Driver installation file
└── test/
    ├── S001Tests.cpp
    └── S002Tests.cpp
```

### 9.2 Required FPGA Side Files

```
FPGA_Implementation/
├── src/
│   ├── top_level.vhd              # Top level entity
│   ├── uart_controller.vhd        # UART interface
│   ├── frame_parser.vhd           # ZES frame parsing
│   ├── s001_handler.vhd           # S001 command handler
│   ├── s002_handler.vhd           # S002 command handler
│   ├── fram_controller.vhd        # FRAM interface
│   └── crc8_calculator.vhd        # CRC calculation
├── constraints/
│   └── timing_constraints.xdc     # Timing constraints
└── testbench/
    ├── s001_tb.vhd
    └── s002_tb.vhd
```

## 10. Next Steps

After successful implementation of S001 and S002:

1. **Implement U-series commands** (U001-U006)
2. **Add A-series commands** (A001-A005)
3. **Implement W-series commands** (W001-W002)
4. **Add advanced error handling**
5. **Optimize performance**
6. **Prepare for production deployment**

## 11. Conclusion

This implementation guide provides a comprehensive roadmap for integrating the FTDI driver (`CDM2123620_Setup.exe`) with Windows User-Mode Driver Framework (UMDF 2) and implementing the critical S001 and S002 commands.

**Key Success Factors**:
- Filter driver approach preserves FTDI functionality
- Modular design enables easy extension
- Comprehensive testing ensures reliability
- Clear separation between PC and FPGA responsibilities

The implementation establishes a solid foundation for the complete RS485 communication system while maintaining compatibility with existing FTDI infrastructure.
