#include <windows.h>
#include <iostream>
#include <locale>
#include <codecvt>
#include <iomanip>
#include <thread>
#include <chrono>
#include "FTDIInstaller.h"
#include "RS485Driver.h"

/**
 * @brief Convert narrow string to wide string
 */
std::wstring stringToWstring(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

/**
 * @brief Display application banner
 */
void displayBanner() {
    std::wcout << L"========================================" << std::endl;
    std::wcout << L"    RS485 Communication Application    " << std::endl;
    std::wcout << L"         Version 1.0.0                 " << std::endl;
    std::wcout << L"========================================" << std::endl;
    std::wcout << std::endl;
}

/**
 * @brief Check and install FTDI driver if needed
 */
bool checkAndInstallFTDIDriver() {
    std::wcout << L"Checking FTDI driver installation..." << std::endl;

    auto installResult = FTDIInstaller::InstallFTDIDriver();
    std::wstring resultMessage = FTDIInstaller::InstallResultToString(installResult);

    switch (installResult) {
        case FTDIInstaller::InstallResult::SUCCESS:
            std::wcout << L"✓ FTDI driver installed successfully!" << std::endl;
            return true;

        case FTDIInstaller::InstallResult::ALREADY_INSTALLED:
            std::wcout << L"✓ FTDI driver already installed and working." << std::endl;
            return true;

        case FTDIInstaller::InstallResult::INSUFFICIENT_PRIVILEGES:
            MessageBoxW(NULL, L"Please run this application as Administrator to install FTDI drivers.\n\nRight-click the executable and select 'Run as administrator'.",
                       L"Administrator Required", MB_OK | MB_ICONERROR);
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;

        case FTDIInstaller::InstallResult::EXTRACTION_FAILED:
            MessageBoxW(NULL, L"Failed to extract embedded FTDI driver files.\n\nPlease check if the application file is corrupted.",
                       L"Extraction Error", MB_OK | MB_ICONERROR);
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;

        case FTDIInstaller::InstallResult::INSTALLATION_FAILED:
            MessageBoxW(NULL, L"Failed to install FTDI driver.\n\nPlease check Windows compatibility and try again.",
                       L"Installation Error", MB_OK | MB_ICONERROR);
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;

        case FTDIInstaller::InstallResult::DEVICE_NOT_FOUND:
            MessageBoxW(NULL, L"No FTDI device found.\n\nPlease connect your USB-RS485 converter and try again.",
                       L"Device Not Found", MB_OK | MB_ICONWARNING);
            std::wcout << L"⚠ " << resultMessage << std::endl;
            return false;

        default:
            MessageBoxW(NULL, L"Unknown error during FTDI driver installation.\n\nPlease contact technical support.",
                       L"Unknown Error", MB_OK | MB_ICONERROR);
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;
    }
}

/**
 * @brief Display available devices
 */
void displayAvailableDevices() {
    std::wcout << L"\nScanning for available devices..." << std::endl;
    
    std::vector<DeviceInfo> devices;
    RS485Error result = RS485Driver::enumerateDevices(devices);
    
    if (result != RS485Error::SUCCESS) {
        std::wcout << L"Failed to enumerate devices." << std::endl;
        return;
    }
    
    if (devices.empty()) {
        std::wcout << L"No RS485 devices found." << std::endl;
        std::wcout << L"Please check that your USB-RS485 converter is connected." << std::endl;
        return;
    }
    
    std::wcout << L"Found " << devices.size() << L" device(s):" << std::endl;
    for (size_t i = 0; i < devices.size(); ++i) {
        const auto& device = devices[i];
        std::wcout << L"  [" << (i + 1) << L"] " << device.devicePath;
        if (!device.description.empty()) {
            std::wcout << L" - " << device.description;
        }
        if (device.isConnected) {
            std::wcout << L" (Connected)";
        }
        std::wcout << std::endl;
    }
}

/**
 * @brief Test basic RS485 commands
 */
void testBasicCommands(RS485Driver& driver) {
    std::wcout << L"\n=== Testing Basic RS485 Commands ===" << std::endl;
    
    // Test S001 command (set slave address)
    std::wcout << L"Testing S001 command (set slave address to 5)..." << std::endl;
    RS485Error result = driver.configureSystemSettings("S001", 5);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ S001 command sent successfully" << std::endl;
    } else {
        std::wcout << L"✗ S001 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Wait a moment for response
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test S002 command (set baud rate)
    std::wcout << L"Testing S002 command (set baud rate to 115200)..." << std::endl;
    result = driver.configureSystemSettings("S002", 115200);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ S002 command sent successfully" << std::endl;
    } else {
        std::wcout << L"✗ S002 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Wait a moment for response
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test U001 command (set SEL detection threshold)
    std::wcout << L"Testing U001 command (set SEL threshold to 250mA)..." << std::endl;
    result = driver.configureUserSettings("U001", 250);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ U001 command sent successfully" << std::endl;
    } else {
        std::wcout << L"✗ U001 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Display buffer status
    BufferStatus bufferStatus;
    if (driver.getBufferStatus(bufferStatus) == RS485Error::SUCCESS) {
        std::wcout << L"\nBuffer Status:" << std::endl;
        std::wcout << L"  Uplink: " << bufferStatus.uplinkUsed << L"/" << bufferStatus.uplinkCapacity 
                  << L" (" << std::fixed << std::setprecision(1) << bufferStatus.uplinkUsagePercent << L"%)" << std::endl;
        std::wcout << L"  Downlink: " << bufferStatus.downlinkUsed << L"/" << bufferStatus.downlinkCapacity 
                  << L" (" << std::fixed << std::setprecision(1) << bufferStatus.downlinkUsagePercent << L"%)" << std::endl;
    }
}

/**
 * @brief Main application entry point
 */
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Allocate console for output
    AllocConsole();
    FILE* pCout;
    freopen_s(&pCout, "CONOUT$", "w", stdout);
    FILE* pCin;
    freopen_s(&pCin, "CONIN$", "r", stdin);
    FILE* pCerr;
    freopen_s(&pCerr, "CONOUT$", "w", stderr);
    
    // Set console title
    SetConsoleTitleW(L"RS485 Communication Application");
    
    // Set locale for proper Unicode output
    std::locale::global(std::locale(""));
    std::wcout.imbue(std::locale());
    
    try {
        // Display banner
        displayBanner();
        
        // Check and install FTDI driver
        if (!checkAndInstallFTDIDriver()) {
            std::wcout << L"\nPress any key to exit..." << std::endl;
            std::cin.get();
            return 1;
        }
        
        // Display available devices
        displayAvailableDevices();
        
        // Initialize RS485 driver
        std::wcout << L"\nInitializing RS485 communication..." << std::endl;
        RS485Driver driver;
        
        RS485Error initResult = driver.Initialize();
        if (initResult != RS485Error::SUCCESS) {
            std::wcout << L"✗ Failed to initialize RS485 communication: " 
                      << stringToWstring(driver.getErrorString(initResult)) << std::endl;
            std::wcout << L"\nPress any key to exit..." << std::endl;
            std::cin.get();
            return 1;
        }
        
        std::wcout << L"✓ RS485 communication initialized successfully!" << std::endl;
        
        // Test basic commands
        testBasicCommands(driver);
        
        // Run main application loop
        std::wcout << L"\n=== RS485 Communication Ready ===" << std::endl;
        int exitCode = driver.RunApplication();
        
        // Cleanup
        driver.closePort();
        std::wcout << L"Application closed successfully." << std::endl;
        
        return exitCode;
        
    } catch (const std::exception& e) {
        std::wcout << L"Exception occurred: " << stringToWstring(e.what()) << std::endl;
        MessageBoxA(NULL, e.what(), "Application Error", MB_OK | MB_ICONERROR);
        return 1;
    } catch (...) {
        std::wcout << L"Unknown exception occurred." << std::endl;
        MessageBoxW(NULL, L"An unknown error occurred.", L"Application Error", MB_OK | MB_ICONERROR);
        return 1;
    }
}
