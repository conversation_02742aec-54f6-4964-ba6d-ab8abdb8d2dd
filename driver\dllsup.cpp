#include <windows.h>
#include <wudfddi.h>
#include "RS485FilterDriver.h"

// Global driver instance
CRS485FilterDriver* g_pRS485Driver = nullptr;

/**
 * @brief DLL entry point
 */
BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID lpReserved)
{
    UNREFERENCED_PARAMETER(hModule);
    UNREFERENCED_PARAMETER(lpReserved);

    switch (dwReason)
    {
    case DLL_PROCESS_ATTACH:
        // Initialize driver instance
        g_pRS485Driver = new CRS485FilterDriver();
        if (!g_pRS485Driver) {
            return FALSE;
        }
        break;

    case DLL_PROCESS_DETACH:
        // Cleanup driver instance
        if (g_pRS485Driver) {
            g_pRS485Driver->Release();
            g_pRS485Driver = nullptr;
        }
        break;
    }

    return TRUE;
}

/**
 * @brief DLL can unload now
 */
STDAPI DllCanUnloadNow()
{
    return S_OK;
}

/**
 * @brief Get DLL version
 */
STDAPI DllGetVersion(DLLVERSIONINFO* pdvi)
{
    if (!pdvi || pdvi->cbSize != sizeof(DLLVERSIONINFO)) {
        return E_INVALIDARG;
    }

    pdvi->dwMajorVersion = 1;
    pdvi->dwMinorVersion = 0;
    pdvi->dwBuildNumber = 0;
    pdvi->dwPlatformID = DLLVER_PLATFORM_WINDOWS;

    return S_OK;
}

/**
 * @brief UMDF driver entry point
 */
extern "C" HRESULT __stdcall DllGetActivationFactory(
    _In_ HSTRING activatableClassId,
    _COM_Outptr_ IActivationFactory** factory
)
{
    UNREFERENCED_PARAMETER(activatableClassId);
    UNREFERENCED_PARAMETER(factory);
    
    return E_NOTIMPL;
}
