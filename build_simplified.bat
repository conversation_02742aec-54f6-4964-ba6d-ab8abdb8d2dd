@echo off
echo ========================================
echo   RS485 UMDF Driver - Simplified Build
echo ========================================
echo.

REM Create build directories
if not exist build mkdir build
if not exist build\driver mkdir build\driver
if not exist build\application mkdir build\application

echo Checking for C++ compiler...

REM Try to find and use any available C++ compiler
set COMPILER_FOUND=0

REM Check for cl.exe (MSVC)
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found Microsoft Visual C++ Compiler
    set COMPILER_FOUND=1
    goto :build_with_msvc
)

REM Check for g++.exe (MinGW)
where g++.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found MinGW G++ Compiler
    set COMPILER_FOUND=1
    goto :build_with_mingw
)

if %COMPILER_FOUND% EQU 0 (
    echo ERROR: No C++ compiler found!
    echo.
    echo Please install one of the following:
    echo 1. Visual Studio 2019/2022 with C++ tools
    echo 2. MinGW-w64
    echo 3. Build Tools for Visual Studio
    echo.
    pause
    exit /b 1
)

:build_with_msvc
echo.
echo ========================================
echo Building with Microsoft Visual C++
echo ========================================
echo.

echo Compiling UMDF Driver (Simulation)...
cl /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
   /I include /I driver ^
   driver\RS485FilterDriver.cpp ^
   /Fo:build\driver\ /Fe:build\driver\RS485Filter_Sim.exe ^
   setupapi.lib advapi32.lib user32.lib kernel32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Driver compilation failed!
    goto :error_exit
)

echo Compiling User Application...
cl /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
   /I include ^
   src\UMDFApplication.cpp src\RS485DriverInterface.cpp src\FTDIInstaller.cpp ^
   /Fe:build\application\RS485_UMDF_Application.exe ^
   setupapi.lib newdev.lib advapi32.lib user32.lib kernel32.lib version.lib cfgmgr32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Application compilation failed!
    goto :error_exit
)

goto :build_success

:build_with_mingw
echo.
echo ========================================
echo Building with MinGW G++
echo ========================================
echo.

echo Compiling UMDF Driver (Simulation)...
g++ -std=c++17 -DWIN32 -D_WINDOWS -DUNICODE -D_UNICODE ^
    -I include -I driver ^
    driver/RS485FilterDriver.cpp ^
    -o build/driver/RS485Filter_Sim.exe ^
    -lsetupapi -ladvapi32 -luser32 -lkernel32

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Driver compilation failed!
    goto :error_exit
)

echo Compiling User Application...
g++ -std=c++17 -DWIN32 -D_WINDOWS -DUNICODE -D_UNICODE ^
    -I include ^
    src/UMDFApplication.cpp src/RS485DriverInterface.cpp src/FTDIInstaller.cpp ^
    -o build/application/RS485_UMDF_Application.exe ^
    -lsetupapi -lnewdev -ladvapi32 -luser32 -lkernel32 -lversion -lcfgmgr32

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Application compilation failed!
    goto :error_exit
)

goto :build_success

:build_success
echo.
echo ========================================
echo Build SUCCESS!
echo ========================================
echo.
echo Output files:
if exist build\driver\RS485Filter_Sim.exe (
    echo ✓ Driver simulation: build\driver\RS485Filter_Sim.exe
    dir build\driver\RS485Filter_Sim.exe | find "RS485Filter_Sim.exe"
)
if exist build\application\RS485_UMDF_Application.exe (
    echo ✓ User application: build\application\RS485_UMDF_Application.exe
    dir build\application\RS485_UMDF_Application.exe | find "RS485_UMDF_Application.exe"
)
echo.
echo Note: This is a simplified build for testing.
echo For production UMDF driver, use the full WDK build environment.
echo.
echo Ready to test! Run: build\application\RS485_UMDF_Application.exe
echo.
goto :end

:error_exit
echo.
echo ========================================
echo Build FAILED!
echo ========================================
echo.
echo Check the error messages above for details.
echo.

:end
echo Press any key to exit...
pause >nul
