#include "FTDIInstaller.h"
#include "resource.h"
#include <setupapi.h>
#include <newdev.h>
#include <shlobj.h>
#include <cfgmgr32.h>
#include <iostream>
#include <sstream>

#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "newdev.lib")
#pragma comment(lib, "advapi32.lib")

// Static member definitions
constexpr wchar_t FTDIInstaller::TEMP_DIR_NAME[];
constexpr wchar_t FTDIInstaller::FTDI_HARDWARE_ID[];

FTDIInstaller::InstallResult FTDIInstaller::InstallFTDIDriver() {
    // Check if running as administrator
    if (!CheckAdminPrivileges()) {
        return InstallResult::INSUFFICIENT_PRIVILEGES;
    }
    
    // Check if FTDI driver is already installed and working
    if (IsFTDIDriverInstalled()) {
        return InstallResult::ALREADY_INSTALLED;
    }
    
    // Extract embedded driver files
    if (!ExtractEmbeddedFiles()) {
        return InstallResult::EXTRACTION_FAILED;
    }
    
    // Install the driver
    std::wstring tempDir = GetTempDirectory();
    std::wstring infPath = tempDir + L"\\ftdibus.inf";
    
    if (!InstallDriverFromINF(infPath)) {
        CleanupTempFiles();
        return InstallResult::INSTALLATION_FAILED;
    }
    
    // Verify installation
    if (!IsFTDIDriverInstalled()) {
        CleanupTempFiles();
        return InstallResult::INSTALLATION_FAILED;
    }
    
    CleanupTempFiles();
    return InstallResult::SUCCESS;
}

bool FTDIInstaller::IsFTDIDriverInstalled() {
    return IsFTDIDevicePresent() && !FindFTDIComPort().empty();
}

bool FTDIInstaller::ExtractEmbeddedFiles() {
    std::wstring tempDir = GetTempDirectory();
    
    // Create temporary directory
    if (!CreateDirectoryW(tempDir.c_str(), NULL)) {
        DWORD error = GetLastError();
        if (error != ERROR_ALREADY_EXISTS) {
            return false;
        }
    }
    
    // Extract all embedded files
    bool success = true;
    success &= ExtractResource(IDR_FTDIBUS_SYS, tempDir + L"\\ftdibus.sys");
    success &= ExtractResource(IDR_FTDIPORT_SYS, tempDir + L"\\ftdiport.sys");
    success &= ExtractResource(IDR_FTDIBUS_INF, tempDir + L"\\ftdibus.inf");
    success &= ExtractResource(IDR_FTDIPORT_INF, tempDir + L"\\ftdiport.inf");
    success &= ExtractResource(IDR_FTD2XX_DLL, tempDir + L"\\ftd2xx.dll");
    
    return success;
}

bool FTDIInstaller::ExtractResource(int resourceId, const std::wstring& outputPath) {
    HRSRC hResource = FindResource(NULL, MAKEINTRESOURCE(resourceId), RT_RCDATA);
    if (!hResource) return false;
    
    HGLOBAL hLoadedResource = LoadResource(NULL, hResource);
    if (!hLoadedResource) return false;
    
    LPVOID pLockedResource = LockResource(hLoadedResource);
    if (!pLockedResource) return false;
    
    DWORD dwResourceSize = SizeofResource(NULL, hResource);
    if (dwResourceSize == 0) return false;
    
    // Write to file
    HANDLE hFile = CreateFileW(outputPath.c_str(), GENERIC_WRITE, 0, NULL, 
                              CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) return false;
    
    DWORD dwBytesWritten;
    BOOL bResult = WriteFile(hFile, pLockedResource, dwResourceSize, &dwBytesWritten, NULL);
    CloseHandle(hFile);
    
    return bResult && (dwBytesWritten == dwResourceSize);
}

bool FTDIInstaller::InstallDriverFromINF(const std::wstring& infPath) {
    // Use Windows API to install driver
    BOOL rebootRequired = FALSE;
    
    BOOL result = UpdateDriverForPlugAndPlayDevicesW(
        NULL,                           // Parent window
        FTDI_HARDWARE_ID,              // Hardware ID for FTDI device
        infPath.c_str(),               // INF file path
        INSTALLFLAG_FORCE,             // Installation flags
        &rebootRequired                // Reboot required flag
    );
    
    if (rebootRequired) {
        MessageBoxW(NULL, L"System reboot required to complete driver installation.", 
                   L"Driver Installation", MB_OK | MB_ICONINFORMATION);
    }
    
    return result != FALSE;
}

std::wstring FTDIInstaller::GetTempDirectory() {
    wchar_t tempPath[MAX_PATH];
    GetTempPathW(MAX_PATH, tempPath);
    return std::wstring(tempPath) + TEMP_DIR_NAME;
}

bool FTDIInstaller::CheckAdminPrivileges() {
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    return isAdmin != FALSE;
}

bool FTDIInstaller::IsFTDIDevicePresent(uint32_t vendorId, uint32_t productId) {
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(NULL, L"USB", NULL, 
                                                 DIGCF_PRESENT | DIGCF_ALLCLASSES);
    if (deviceInfoSet == INVALID_HANDLE_VALUE) return false;
    
    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    bool found = false;
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        wchar_t hardwareId[256];
        if (SetupDiGetDeviceRegistryPropertyW(deviceInfoSet, &deviceInfoData, 
                                            SPDRP_HARDWAREID, NULL, 
                                            (PBYTE)hardwareId, sizeof(hardwareId), NULL)) {
            
            // Create expected hardware ID string
            std::wstringstream expectedId;
            expectedId << L"VID_" << std::hex << std::uppercase << vendorId 
                      << L"&PID_" << std::hex << std::uppercase << productId;
            
            if (wcsstr(hardwareId, expectedId.str().c_str())) {
                found = true;
                break;
            }
        }
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    return found;
}

std::wstring FTDIInstaller::FindFTDIComPort(uint32_t vendorId, uint32_t productId) {
    std::vector<FTDIDeviceInfo> devices;
    if (!EnumerateFTDIDevices(devices)) {
        return L"";
    }
    
    for (const auto& device : devices) {
        if (device.vendorId == vendorId && device.productId == productId && 
            device.isPortAvailable && !device.comPort.empty()) {
            return device.comPort;
        }
    }
    
    return L"";
}

bool FTDIInstaller::EnumerateFTDIDevices(std::vector<FTDIDeviceInfo>& devices) {
    devices.clear();
    
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(NULL, L"USB", NULL, 
                                                 DIGCF_PRESENT | DIGCF_ALLCLASSES);
    if (deviceInfoSet == INVALID_HANDLE_VALUE) return false;
    
    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        wchar_t hardwareId[256];
        if (SetupDiGetDeviceRegistryPropertyW(deviceInfoSet, &deviceInfoData, 
                                            SPDRP_HARDWAREID, NULL, 
                                            (PBYTE)hardwareId, sizeof(hardwareId), NULL)) {
            
            // Check if this is an FTDI device
            if (wcsstr(hardwareId, L"VID_0403")) {
                FTDIDeviceInfo deviceInfo = {};
                
                // Extract vendor and product IDs
                wchar_t* vidPos = wcsstr(hardwareId, L"VID_");
                wchar_t* pidPos = wcsstr(hardwareId, L"PID_");
                
                if (vidPos && pidPos) {
                    deviceInfo.vendorId = wcstoul(vidPos + 4, NULL, 16);
                    deviceInfo.productId = wcstoul(pidPos + 4, NULL, 16);
                }
                
                // Get device properties
                GetDeviceProperty(deviceInfoSet, &deviceInfoData, SPDRP_DEVICEDESC, deviceInfo.description);
                GetDeviceProperty(deviceInfoSet, &deviceInfoData, SPDRP_LOCATION_INFORMATION, deviceInfo.serialNumber);
                GetDeviceComPort(deviceInfoSet, &deviceInfoData, deviceInfo.comPort);
                
                deviceInfo.isDriverLoaded = IsDeviceDriverLoaded(deviceInfoSet, &deviceInfoData);
                deviceInfo.isPortAvailable = !deviceInfo.comPort.empty();
                
                devices.push_back(deviceInfo);
            }
        }
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    return true;
}

bool FTDIInstaller::GetDeviceProperty(HDEVINFO deviceInfoSet,
                                     SP_DEVINFO_DATA* deviceInfoData,
                                     DWORD property,
                                     std::wstring& value) {
    wchar_t buffer[256];
    DWORD bufferSize = sizeof(buffer);

    if (SetupDiGetDeviceRegistryPropertyW(deviceInfoSet, deviceInfoData,
                                         property, NULL,
                                         (PBYTE)buffer, bufferSize, NULL)) {
        value = buffer;
        return true;
    }

    value.clear();
    return false;
}

bool FTDIInstaller::GetDeviceComPort(HDEVINFO deviceInfoSet,
                                    SP_DEVINFO_DATA* deviceInfoData,
                                    std::wstring& comPort) {
    // Try to get COM port from device registry
    HKEY hKey = SetupDiOpenDevRegKey(deviceInfoSet, deviceInfoData,
                                    DICS_FLAG_GLOBAL, 0, DIREG_DEV, KEY_READ);
    if (hKey == INVALID_HANDLE_VALUE) {
        comPort.clear();
        return false;
    }

    wchar_t portName[256];
    DWORD portNameSize = sizeof(portName);
    DWORD type;

    LONG result = RegQueryValueExW(hKey, L"PortName", NULL, &type,
                                  (LPBYTE)portName, &portNameSize);
    RegCloseKey(hKey);

    if (result == ERROR_SUCCESS && type == REG_SZ) {
        comPort = portName;
        return true;
    }

    comPort.clear();
    return false;
}

bool FTDIInstaller::IsDeviceDriverLoaded(HDEVINFO deviceInfoSet,
                                        SP_DEVINFO_DATA* deviceInfoData) {
    DWORD status, problem;

    if (CM_Get_DevNode_Status(&status, &problem, deviceInfoData->DevInst, 0) == CR_SUCCESS) {
        // Check if device is working properly
        return (status & DN_DRIVER_LOADED) && !(status & DN_HAS_PROBLEM);
    }

    return false;
}

bool FTDIInstaller::CleanupTempFiles() {
    std::wstring tempDir = GetTempDirectory();

    // Delete individual files
    DeleteFileW((tempDir + L"\\ftdibus.sys").c_str());
    DeleteFileW((tempDir + L"\\ftdiport.sys").c_str());
    DeleteFileW((tempDir + L"\\ftdibus.inf").c_str());
    DeleteFileW((tempDir + L"\\ftdiport.inf").c_str());
    DeleteFileW((tempDir + L"\\ftd2xx.dll").c_str());

    // Remove directory
    RemoveDirectoryW(tempDir.c_str());

    return true;
}

bool FTDIInstaller::GetFTDIDriverVersion(std::wstring& version) {
    // Try to get version from driver file
    std::wstring systemDir;
    wchar_t sysDir[MAX_PATH];
    if (GetSystemDirectoryW(sysDir, MAX_PATH)) {
        systemDir = sysDir;
        std::wstring driverPath = systemDir + L"\\drivers\\ftdibus.sys";

        DWORD handle;
        DWORD versionSize = GetFileVersionInfoSizeW(driverPath.c_str(), &handle);
        if (versionSize > 0) {
            std::vector<BYTE> versionData(versionSize);
            if (GetFileVersionInfoW(driverPath.c_str(), handle, versionSize, versionData.data())) {
                VS_FIXEDFILEINFO* fileInfo;
                UINT fileInfoSize;
                if (VerQueryValueW(versionData.data(), L"\\", (LPVOID*)&fileInfo, &fileInfoSize)) {
                    std::wstringstream versionStream;
                    versionStream << HIWORD(fileInfo->dwFileVersionMS) << L"."
                                 << LOWORD(fileInfo->dwFileVersionMS) << L"."
                                 << HIWORD(fileInfo->dwFileVersionLS) << L"."
                                 << LOWORD(fileInfo->dwFileVersionLS);
                    version = versionStream.str();
                    return true;
                }
            }
        }
    }

    version = L"Unknown";
    return false;
}

std::wstring FTDIInstaller::InstallResultToString(InstallResult result) {
    switch (result) {
        case InstallResult::SUCCESS:
            return L"Driver installed successfully";
        case InstallResult::ALREADY_INSTALLED:
            return L"Driver already installed and working";
        case InstallResult::EXTRACTION_FAILED:
            return L"Failed to extract embedded driver files";
        case InstallResult::INSTALLATION_FAILED:
            return L"Driver installation failed";
        case InstallResult::INSUFFICIENT_PRIVILEGES:
            return L"Administrator privileges required";
        case InstallResult::DEVICE_NOT_FOUND:
            return L"FTDI device not found";
        case InstallResult::UNKNOWN_ERROR:
        default:
            return L"Unknown error occurred";
    }
}
