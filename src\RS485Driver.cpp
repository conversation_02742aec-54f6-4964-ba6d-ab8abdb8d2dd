#include "RS485Driver.h"
#include "FTDIInstaller.h"
#include <windows.h>
#include <iostream>
#include <sstream>
#include <chrono>
#include <algorithm>

/**
 * @brief Internal implementation class for RS485Driver
 * 
 * This class contains the actual implementation details, following the PIMPL idiom
 * to hide implementation complexity from the public interface.
 */
class RS485Driver::Impl {
public:
    Impl() : 
        m_isPortOpen(false),
        m_serialHandle(INVALID_HANDLE_VALUE),
        m_currentSlaveAddress(0),
        m_bufferOverflowPolicy(BufferOverflowPolicy::DISCARD_OLDEST),
        m_isReceiveThreadRunning(false),
        m_shouldStopReceiveThread(false) {
        
        // Initialize buffer capacities
        m_uplinkBuffer.reserve(RS485_UPLINK_BUFFER_SIZE);
        m_downlinkBuffer.reserve(RS485_DOWNLINK_BUFFER_SIZE);
        
        // Initialize performance metrics
        std::memset(&m_performanceMetrics, 0, sizeof(m_performanceMetrics));
    }

    ~Impl() {
        closePort();
    }

    // Port management
    RS485Error openPort(const std::wstring& devicePath);
    RS485Error closePort();
    bool isPortOpen() const { return m_isPortOpen; }

    // Buffer management
    RS485Error getBufferStatus(BufferStatus& status);
    RS485Error clearBuffer(BufferType bufferType);
    bool checkUplinkBufferSpace();
    bool checkDownlinkBufferSpace();

    // Frame processing
    RS485Error sendFrame(const RS485Frame& frame);
    RS485Error receiveFrame(RS485Frame& frame, uint32_t timeout);
    void processReceivedFrame(const RS485Frame& frame);

    // Command processing
    RS485Error sendCommand(const std::string& commandKey, uint64_t value, uint8_t slaveAddress = 0);
    RS485Error sendSystemCommand(const std::string& commandKey, uint64_t value);
    RS485Error sendUserCommand(const std::string& commandKey, uint64_t value);
    RS485Error sendRequestCommand(const std::string& commandKey, uint8_t slaveAddress);

    // Utility functions
    uint8_t createIdByte(FunctionCode functionCode, uint8_t deviceAddress);
    FunctionCode extractFunctionCode(uint8_t idByte);
    uint8_t extractDeviceAddress(uint8_t idByte);
    const char* getErrorString(RS485Error error);

    // Thread management
    void startReceiveThread();
    void stopReceiveThread();
    void receiveThreadFunction();

    // Member variables
    bool m_isPortOpen;
    HANDLE m_serialHandle;
    std::wstring m_devicePath;
    uint8_t m_currentSlaveAddress;
    BufferOverflowPolicy m_bufferOverflowPolicy;

    // Buffers
    std::vector<std::vector<uint8_t>> m_uplinkBuffer;
    std::vector<std::vector<uint8_t>> m_downlinkBuffer;
    std::mutex m_bufferMutex;

    // Threading
    std::thread m_receiveThread;
    std::atomic<bool> m_isReceiveThreadRunning;
    std::atomic<bool> m_shouldStopReceiveThread;

    // Callbacks
    ErrorCallbackFn m_errorCallback;
    BufferThresholdCallbackFn m_bufferThresholdCallback;
    DataReadyCallbackFn m_dataReadyCallback;

    // Performance tracking
    PerformanceMetrics m_performanceMetrics;
    std::chrono::steady_clock::time_point m_lastActivityTime;

    // Port configuration
    PortInfo m_portInfo;
};

RS485Error RS485Driver::Impl::openPort(const std::wstring& devicePath) {
    if (m_isPortOpen) {
        return RS485Error::DEVICE_ALREADY_OPEN;
    }

    // Open serial port
    m_serialHandle = CreateFileW(
        devicePath.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL | FILE_FLAG_OVERLAPPED,
        NULL
    );

    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        if (error == ERROR_FILE_NOT_FOUND) {
            return RS485Error::DEVICE_NOT_FOUND;
        }
        return RS485Error::DRIVER_ERROR;
    }

    // Configure serial port
    DCB dcb = {};
    dcb.DCBlength = sizeof(DCB);
    
    if (!GetCommState(m_serialHandle, &dcb)) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
        return RS485Error::DRIVER_ERROR;
    }

    // Set default RS485 parameters (8N1, 9600 baud)
    dcb.BaudRate = CBR_9600;
    dcb.ByteSize = 8;
    dcb.Parity = NOPARITY;
    dcb.StopBits = ONESTOPBIT;
    dcb.fBinary = TRUE;
    dcb.fParity = FALSE;
    dcb.fOutxCtsFlow = FALSE;
    dcb.fOutxDsrFlow = FALSE;
    dcb.fDtrControl = DTR_CONTROL_DISABLE;
    dcb.fDsrSensitivity = FALSE;
    dcb.fTXContinueOnXoff = FALSE;
    dcb.fOutX = FALSE;
    dcb.fInX = FALSE;
    dcb.fErrorChar = FALSE;
    dcb.fNull = FALSE;
    dcb.fRtsControl = RTS_CONTROL_DISABLE;
    dcb.fAbortOnError = FALSE;

    if (!SetCommState(m_serialHandle, &dcb)) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
        return RS485Error::DRIVER_ERROR;
    }

    // Set timeouts
    COMMTIMEOUTS timeouts = {};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = 100;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 100;
    timeouts.WriteTotalTimeoutMultiplier = 10;

    if (!SetCommTimeouts(m_serialHandle, &timeouts)) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
        return RS485Error::DRIVER_ERROR;
    }

    // Clear buffers
    PurgeComm(m_serialHandle, PURGE_RXCLEAR | PURGE_TXCLEAR);

    // Update state
    m_devicePath = devicePath;
    m_isPortOpen = true;
    
    // Update port info
    m_portInfo.portName = devicePath;
    m_portInfo.baudRate = 9600;
    m_portInfo.dataBits = 8;
    m_portInfo.stopBits = 1;
    m_portInfo.parity = 0;
    m_portInfo.isOpen = true;

    // Start receive thread
    startReceiveThread();

    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::Impl::closePort() {
    if (!m_isPortOpen) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // Stop receive thread
    stopReceiveThread();

    // Close serial port
    if (m_serialHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
    }

    // Update state
    m_isPortOpen = false;
    m_devicePath.clear();
    m_portInfo.isOpen = false;

    // Clear buffers
    std::lock_guard<std::mutex> lock(m_bufferMutex);
    m_uplinkBuffer.clear();
    m_downlinkBuffer.clear();

    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::Impl::getBufferStatus(BufferStatus& status) {
    std::lock_guard<std::mutex> lock(m_bufferMutex);
    
    status.uplinkUsed = static_cast<uint32_t>(m_uplinkBuffer.size());
    status.uplinkCapacity = RS485_UPLINK_BUFFER_SIZE;
    status.downlinkUsed = static_cast<uint32_t>(m_downlinkBuffer.size());
    status.downlinkCapacity = RS485_DOWNLINK_BUFFER_SIZE;
    
    status.isUplinkFull = (status.uplinkUsed >= status.uplinkCapacity);
    status.isDownlinkFull = (status.downlinkUsed >= status.downlinkCapacity);
    
    status.uplinkUsagePercent = (static_cast<double>(status.uplinkUsed) / status.uplinkCapacity) * 100.0;
    status.downlinkUsagePercent = (static_cast<double>(status.downlinkUsed) / status.downlinkCapacity) * 100.0;
    
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::Impl::clearBuffer(BufferType bufferType) {
    std::lock_guard<std::mutex> lock(m_bufferMutex);
    
    switch (bufferType) {
        case BufferType::UPLINK:
            m_uplinkBuffer.clear();
            break;
        case BufferType::DOWNLINK:
            m_downlinkBuffer.clear();
            break;
        case BufferType::BOTH:
            m_uplinkBuffer.clear();
            m_downlinkBuffer.clear();
            break;
    }
    
    return RS485Error::SUCCESS;
}

bool RS485Driver::Impl::checkUplinkBufferSpace() {
    std::lock_guard<std::mutex> lock(m_bufferMutex);
    return m_uplinkBuffer.size() < RS485_UPLINK_BUFFER_SIZE;
}

bool RS485Driver::Impl::checkDownlinkBufferSpace() {
    std::lock_guard<std::mutex> lock(m_bufferMutex);
    return m_downlinkBuffer.size() < RS485_DOWNLINK_BUFFER_SIZE;
}

RS485Error RS485Driver::Impl::sendFrame(const RS485Frame& frame) {
    if (!m_isPortOpen) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // Check uplink buffer space
    if (!checkUplinkBufferSpace()) {
        switch (m_bufferOverflowPolicy) {
            case BufferOverflowPolicy::DISCARD_OLDEST:
                {
                    std::lock_guard<std::mutex> lock(m_bufferMutex);
                    if (!m_uplinkBuffer.empty()) {
                        m_uplinkBuffer.erase(m_uplinkBuffer.begin());
                    }
                }
                break;
            case BufferOverflowPolicy::DISCARD_NEWEST:
            case BufferOverflowPolicy::TRIGGER_ERROR:
                return RS485Error::INSUFFICIENT_BUFFER;
        }
    }

    // Send frame to serial port
    DWORD bytesWritten;
    OVERLAPPED overlapped = {};
    overlapped.hEvent = CreateEvent(NULL, TRUE, FALSE, NULL);

    if (!overlapped.hEvent) {
        return RS485Error::DRIVER_ERROR;
    }

    BOOL result = WriteFile(m_serialHandle, &frame, sizeof(RS485Frame), &bytesWritten, &overlapped);

    if (!result) {
        DWORD error = GetLastError();
        if (error == ERROR_IO_PENDING) {
            // Wait for completion
            DWORD waitResult = WaitForSingleObject(overlapped.hEvent, 1000); // 1 second timeout
            if (waitResult == WAIT_OBJECT_0) {
                GetOverlappedResult(m_serialHandle, &overlapped, &bytesWritten, FALSE);
                result = TRUE;
            } else {
                CancelIo(m_serialHandle);
                CloseHandle(overlapped.hEvent);
                return RS485Error::COMMUNICATION_TIMEOUT;
            }
        }
    }

    CloseHandle(overlapped.hEvent);

    if (!result || bytesWritten != sizeof(RS485Frame)) {
        return RS485Error::DRIVER_ERROR;
    }

    // Update performance metrics
    m_performanceMetrics.totalFramesSent++;
    m_lastActivityTime = std::chrono::steady_clock::now();

    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::Impl::sendCommand(const std::string& commandKey, uint64_t value, uint8_t slaveAddress) {
    if (commandKey.length() != 4) {
        return RS485Error::INVALID_PARAMETER;
    }

    // Create frame
    RS485Frame frame = {};
    frame.header = RS485_FRAME_HEADER;
    frame.trailer = RS485_FRAME_TRAILER;

    // Determine function code based on command type
    FunctionCode functionCode;
    if (commandKey[0] == 'S') {
        functionCode = FunctionCode::ASSIGN_DATA;
        slaveAddress = 0; // Broadcast for S-series
    } else if (commandKey[0] == 'U' || commandKey[0] == 'W') {
        functionCode = FunctionCode::ASSIGN_DATA;
        if (slaveAddress == 0) {
            slaveAddress = m_currentSlaveAddress;
        }
    } else if (commandKey[0] == 'A') {
        functionCode = FunctionCode::REQUEST_DATA;
    } else {
        return RS485Error::INVALID_PARAMETER;
    }

    // Create ID byte
    frame.id_byte = createIdByte(functionCode, slaveAddress);

    // Create payload
    RS485DataFormat::createPayload(commandKey.c_str(), value, frame.payload);

    // Calculate CRC
    frame.crc8 = CRC8Calculator::calculateFrameCRC(&frame);

    // Send frame
    return sendFrame(frame);
}

uint8_t RS485Driver::Impl::createIdByte(FunctionCode functionCode, uint8_t deviceAddress) {
    uint8_t idByte = 0;
    idByte |= (static_cast<uint8_t>(functionCode) & 0x07) << 5; // Function code in upper 3 bits
    idByte |= (deviceAddress & 0x1F);                          // Device address in lower 5 bits
    return idByte;
}

FunctionCode RS485Driver::Impl::extractFunctionCode(uint8_t idByte) {
    return static_cast<FunctionCode>((idByte >> 5) & 0x07);
}

uint8_t RS485Driver::Impl::extractDeviceAddress(uint8_t idByte) {
    return idByte & 0x1F;
}

const char* RS485Driver::Impl::getErrorString(RS485Error error) {
    switch (error) {
        case RS485Error::SUCCESS: return "Success";
        case RS485Error::DEVICE_NOT_FOUND: return "Device not found";
        case RS485Error::DEVICE_ALREADY_OPEN: return "Device already open";
        case RS485Error::DEVICE_NOT_OPEN: return "Device not open";
        case RS485Error::INVALID_PARAMETER: return "Invalid parameter";
        case RS485Error::INSUFFICIENT_BUFFER: return "Insufficient buffer space";
        case RS485Error::COMMUNICATION_TIMEOUT: return "Communication timeout";
        case RS485Error::CRC_ERROR: return "CRC error";
        case RS485Error::FRAME_ERROR: return "Frame error";
        case RS485Error::BUFFER_OVERFLOW: return "Buffer overflow";
        case RS485Error::DRIVER_ERROR: return "Driver error";
        case RS485Error::INSUFFICIENT_PRIVILEGES: return "Insufficient privileges";
        case RS485Error::INSTALLATION_FAILED: return "Installation failed";
        case RS485Error::INVALID_FUNCTION_CODE: return "Invalid function code";
        case RS485Error::INVALID_DEVICE_ADDRESS: return "Invalid device address";
        case RS485Error::HARDWARE_ERROR: return "Hardware error";
        default: return "Unknown error";
    }
}

void RS485Driver::Impl::processReceivedFrame(const RS485Frame& frame) {
    // Extract function code and device address
    FunctionCode functionCode = extractFunctionCode(frame.id_byte);
    uint8_t deviceAddress = extractDeviceAddress(frame.id_byte);

    // Parse payload
    char commandKey[5];
    uint64_t value;
    RS485DataFormat::parsePayload(frame.payload, commandKey, &value);

    // Store in downlink buffer if there's space
    if (checkDownlinkBufferSpace()) {
        std::lock_guard<std::mutex> lock(m_bufferMutex);
        std::vector<uint8_t> responseData(frame.payload, frame.payload + RS485_PAYLOAD_SIZE);
        m_downlinkBuffer.push_back(responseData);

        // Notify callback if registered
        if (m_dataReadyCallback) {
            m_dataReadyCallback(deviceAddress, frame.payload, RS485_PAYLOAD_SIZE);
        }
    }
}

// ===== RS485Driver Public Interface Implementation =====

RS485Driver::RS485Driver() : m_pImpl(std::make_unique<Impl>()) {
}

RS485Driver::~RS485Driver() = default;

RS485Error RS485Driver::openPort(const std::wstring& devicePath) {
    return m_pImpl->openPort(devicePath);
}

RS485Error RS485Driver::closePort() {
    return m_pImpl->closePort();
}

bool RS485Driver::isPortOpen() const {
    return m_pImpl->isPortOpen();
}

RS485Error RS485Driver::getPortInfo(PortInfo& info) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }
    info = m_pImpl->m_portInfo;
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::enumerateDevices(std::vector<DeviceInfo>& deviceList) {
    deviceList.clear();

    std::vector<FTDIInstaller::FTDIDeviceInfo> ftdiDevices;
    if (!FTDIInstaller::EnumerateFTDIDevices(ftdiDevices)) {
        return RS485Error::DRIVER_ERROR;
    }

    for (const auto& ftdiDevice : ftdiDevices) {
        DeviceInfo device;
        device.devicePath = ftdiDevice.comPort;
        device.description = ftdiDevice.description;
        device.serialNumber = ftdiDevice.serialNumber;
        device.vendorId = ftdiDevice.vendorId;
        device.productId = ftdiDevice.productId;
        device.isConnected = ftdiDevice.isPortAvailable;
        deviceList.push_back(device);
    }

    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getBufferStatus(BufferStatus& status) {
    return m_pImpl->getBufferStatus(status);
}

RS485Error RS485Driver::checkUplinkBufferFlag(bool& isFull) {
    BufferStatus status;
    RS485Error result = m_pImpl->getBufferStatus(status);
    if (result == RS485Error::SUCCESS) {
        isFull = status.isUplinkFull;
    }
    return result;
}

RS485Error RS485Driver::checkDownlinkBufferFlag(bool& isFull) {
    BufferStatus status;
    RS485Error result = m_pImpl->getBufferStatus(status);
    if (result == RS485Error::SUCCESS) {
        isFull = status.isDownlinkFull;
    }
    return result;
}

RS485Error RS485Driver::clearBuffer(BufferType bufferType) {
    return m_pImpl->clearBuffer(bufferType);
}

RS485Error RS485Driver::setBufferOverflowPolicy(BufferOverflowPolicy policy) {
    m_pImpl->m_bufferOverflowPolicy = policy;
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames) {
    uplinkFrames = RS485_UPLINK_BUFFER_SIZE;
    downlinkFrames = RS485_DOWNLINK_BUFFER_SIZE;
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getHardwareStatus(HardwareStatus& status) {
    status.isConnected = m_pImpl->isPortOpen();
    status.isDriverLoaded = true; // Always true if we're running
    status.signalStrength = 100;  // Placeholder
    status.errorCount = static_cast<uint32_t>(m_pImpl->m_performanceMetrics.totalErrors);

    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    status.lastActivityTime = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();

    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getPerformanceMetrics(PerformanceMetrics& metrics) {
    metrics = m_pImpl->m_performanceMetrics;
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getBaudRate(uint32_t& currentBaudRate) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }
    currentBaudRate = m_pImpl->m_portInfo.baudRate;
    return RS485Error::SUCCESS;
}

RS485Error RS485Driver::getLineStatus(LineStatus& status) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // Get line status from serial port
    DWORD modemStatus;
    if (GetCommModemStatus(m_pImpl->m_serialHandle, &modemStatus)) {
        status.carrierDetect = (modemStatus & MS_RLSD_ON) != 0;
        status.dataSetReady = (modemStatus & MS_DSR_ON) != 0;
        status.ringIndicator = (modemStatus & MS_RING_ON) != 0;
        status.clearToSend = (modemStatus & MS_CTS_ON) != 0;
        status.dataTerminalReady = true; // Assume DTR is set
        status.requestToSend = true;     // Assume RTS is set
        status.signalLevel = 100;        // Placeholder
        return RS485Error::SUCCESS;
    }

    return RS485Error::DRIVER_ERROR;
}

const char* RS485Driver::getErrorString(RS485Error error) const {
    return m_pImpl->getErrorString(error);
}

void RS485Driver::registerErrorCallback(ErrorCallbackFn callback) {
    m_pImpl->m_errorCallback = callback;
}

void RS485Driver::registerBufferThresholdCallback(BufferThresholdCallbackFn callback) {
    m_pImpl->m_bufferThresholdCallback = callback;
}

// ===== MASTER BROADCASTING API =====

RS485Error RS485Driver::configureSystemSettings(const std::string& commandKey, uint64_t value) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // Validate S-series command
    if (commandKey.length() != 4 || commandKey[0] != 'S') {
        return RS485Error::INVALID_PARAMETER;
    }

    // Special handling for S001 (slave address assignment)
    if (commandKey == "S001") {
        uint32_t address = RS485DataFormat::decodeInteger(value);
        if (address < 1 || address > 31) {
            return RS485Error::INVALID_DEVICE_ADDRESS;
        }
        // Update current slave address for future U-series commands
        m_pImpl->m_currentSlaveAddress = static_cast<uint8_t>(address);
    }

    return m_pImpl->sendCommand(commandKey, value, 0); // Broadcast to address 0
}

// ===== MASTER ASSIGN DATA API =====

RS485Error RS485Driver::configureUserSettings(const std::string& commandKey, uint64_t value) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // Validate U-series command
    if (commandKey.length() != 4 || commandKey[0] != 'U') {
        return RS485Error::INVALID_PARAMETER;
    }

    // Use current slave address set by S001
    if (m_pImpl->m_currentSlaveAddress == 0) {
        return RS485Error::INVALID_DEVICE_ADDRESS;
    }

    return m_pImpl->sendCommand(commandKey, value, m_pImpl->m_currentSlaveAddress);
}

RS485Error RS485Driver::modelDataOperation(uint8_t slaveAddress, uint32_t address,
                                          uint8_t* data, bool isWrite, size_t length) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    if (slaveAddress < 1 || slaveAddress > 31) {
        return RS485Error::INVALID_DEVICE_ADDRESS;
    }

    if (!data || length == 0) {
        return RS485Error::INVALID_PARAMETER;
    }

    // For W-series commands, we need to handle data in chunks
    std::string commandKey = isWrite ? "W001" : "W002";

    // For simplicity, handle first 8 bytes of data
    uint64_t value = 0;
    size_t copyLength = std::min(length, sizeof(uint64_t));

    if (isWrite) {
        // Copy data to value for writing
        std::memcpy(&value, data, copyLength);
    }

    // Encode address and value
    uint64_t payload = RS485DataFormat::encodeDualIntegers(address, static_cast<uint32_t>(value));

    RS485Error result = m_pImpl->sendCommand(commandKey, payload, slaveAddress);

    if (!isWrite && result == RS485Error::SUCCESS) {
        // For read operations, we would need to wait for response
        // This is a simplified implementation
        std::vector<uint8_t> responseData;
        result = receiveSlaveResponse(slaveAddress, responseData, true, 1000);
        if (result == RS485Error::SUCCESS && !responseData.empty()) {
            size_t copySize = std::min(length, responseData.size());
            std::memcpy(data, responseData.data(), copySize);
        }
    }

    return result;
}

// ===== MASTER REQUEST API =====

RS485Error RS485Driver::requestData(uint8_t slaveAddress, const std::string& dataKey,
                                   std::vector<uint8_t>& responseData) {
    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    if (slaveAddress < 1 || slaveAddress > 31) {
        return RS485Error::INVALID_DEVICE_ADDRESS;
    }

    // Validate A-series command
    if (dataKey.length() != 4 || dataKey[0] != 'A') {
        return RS485Error::INVALID_PARAMETER;
    }

    // Send request command
    RS485Error result = m_pImpl->sendCommand(dataKey, 0, slaveAddress);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    // Wait for response
    return receiveSlaveResponse(slaveAddress, responseData, true, 1000);
}

// ===== SLAVE RESPONSE API =====

RS485Error RS485Driver::receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                            bool waitForData, uint32_t timeout) {
    responseData.clear();

    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    auto startTime = std::chrono::steady_clock::now();

    while (waitForData) {
        // Check for timeout
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
        if (timeout > 0 && elapsed.count() >= timeout) {
            return RS485Error::COMMUNICATION_TIMEOUT;
        }

        // Check downlink buffer for responses
        {
            std::lock_guard<std::mutex> lock(m_pImpl->m_bufferMutex);
            if (!m_pImpl->m_downlinkBuffer.empty()) {
                // For simplicity, return the first available response
                // In a full implementation, we would filter by slave address
                responseData = m_pImpl->m_downlinkBuffer.front();
                m_pImpl->m_downlinkBuffer.erase(m_pImpl->m_downlinkBuffer.begin());
                return RS485Error::SUCCESS;
            }
        }

        if (!waitForData) {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    return RS485Error::COMMUNICATION_TIMEOUT;
}

void RS485Driver::registerDataReadyCallback(DataReadyCallbackFn callback) {
    m_pImpl->m_dataReadyCallback = callback;
}

// ===== UTILITY FUNCTIONS =====

RS485Error RS485Driver::Initialize() {
    // Check if FTDI driver is installed
    if (!FTDIInstaller::IsFTDIDriverInstalled()) {
        return RS485Error::DRIVER_ERROR;
    }

    // Find FTDI COM port
    std::wstring comPort = FTDIInstaller::FindFTDIComPort();
    if (comPort.empty()) {
        return RS485Error::DEVICE_NOT_FOUND;
    }

    // Open the port
    return openPort(comPort);
}

int RS485Driver::RunApplication() {
    std::wcout << L"RS485 Communication Application Running..." << std::endl;
    std::wcout << L"Press 'q' to quit, 's' for status, 't' for test commands" << std::endl;

    char input;
    while (std::cin >> input) {
        switch (input) {
            case 'q':
            case 'Q':
                std::wcout << L"Shutting down..." << std::endl;
                return 0;

            case 's':
            case 'S':
                {
                    BufferStatus status;
                    if (getBufferStatus(status) == RS485Error::SUCCESS) {
                        std::wcout << L"Buffer Status:" << std::endl;
                        std::wcout << L"  Uplink: " << status.uplinkUsed << L"/" << status.uplinkCapacity << std::endl;
                        std::wcout << L"  Downlink: " << status.downlinkUsed << L"/" << status.downlinkCapacity << std::endl;
                    }
                }
                break;

            case 't':
            case 'T':
                {
                    std::wcout << L"Testing S001 command (set slave address to 5)..." << std::endl;
                    RS485Error result = configureSystemSettings("S001", 5);
                    std::wcout << L"Result: " << getErrorString(result) << std::endl;
                }
                break;

            default:
                std::wcout << L"Unknown command. Press 'q' to quit." << std::endl;
                break;
        }
    }

    return 0;
}

void RS485Driver::setCurrentSlaveAddress(uint8_t address) {
    if (address >= 1 && address <= 31) {
        m_pImpl->m_currentSlaveAddress = address;
    }
}

uint8_t RS485Driver::getCurrentSlaveAddress() const {
    return m_pImpl->m_currentSlaveAddress;
}

RS485Error RS485Driver::detectMultipleDevices(std::vector<uint8_t>& detectedAddresses) {
    detectedAddresses.clear();

    if (!m_pImpl->isPortOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    // This is a simplified implementation
    // In a full implementation, we would send broadcast queries and collect responses

    return RS485Error::SUCCESS;
}
