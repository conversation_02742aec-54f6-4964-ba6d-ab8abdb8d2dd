# RS485 Communication Application

A comprehensive Windows application for RS485 communication with AI-SLDAP devices, featuring embedded FTDI driver support and ZES protocol implementation.

## Features

- **Single Executable**: Complete solution with embedded FTDI VCP driver
- **ZES Protocol Support**: Full implementation of the ZES communication protocol
- **Five API Categories**: 
  - Error Handle API (Management functions)
  - Master Broadcasting API (S-series commands)
  - Master Assign Data API (U-series and W-series commands)
  - Master Request API (A-series commands)
  - Slave Response API (Response handling)
- **Advanced Buffer Management**: Intelligent uplink/downlink buffer handling
- **Windows Integration**: Native Windows driver using WDK User-Mode Framework

## System Requirements

- Windows 7 or later (64-bit)
- Administrator privileges (for driver installation)
- USB-RS485-WE-1800-BT FTDI converter
- Visual Studio 2019/2022 (for building from source)

## Quick Start

### For End Users

1. Download `RS485_Communication_Application.exe`
2. Right-click and select "Run as administrator"
3. The application will automatically install FTDI drivers if needed
4. Connect your USB-RS485 converter
5. The application will detect and initialize the device automatically

### For Developers

#### Prerequisites

1. **Visual Studio 2019 or 2022** with C++ development tools
2. **Windows SDK 10**
3. **CMake 3.16 or later**
4. **Git** (for version control)

#### Building from Source

1. **Clone the repository**:
   ```cmd
   git clone <repository-url>
   cd RS485_driver_development
   ```

2. **Extract FTDI driver files** (if not already done):
   ```cmd
   # Use 7-Zip or similar to extract CDM2123620_Setup.exe
   7z x CDM2123620_Setup.exe -oFTDI_Extracted
   
   # Copy required files to resources directory
   mkdir resources\drivers resources\libs
   copy FTDI_Extracted\ftdibus.sys resources\drivers\
   copy FTDI_Extracted\ftdiport.sys resources\drivers\
   copy FTDI_Extracted\ftdibus.inf resources\drivers\
   copy FTDI_Extracted\ftdiport.inf resources\drivers\
   copy FTDI_Extracted\amd64\ftd2xx.dll resources\libs\
   copy FTDI_Extracted\amd64\ftd2xx.lib resources\libs\
   copy FTDI_Extracted\ftd2xx.h include\
   ```

3. **Build the application**:
   ```cmd
   # Option 1: Use the build script
   build.bat
   
   # Option 2: Manual build
   mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

4. **Output**: The executable will be created at `build\Release\RS485_Communication_Application.exe`

## Usage

### Command Line Interface

The application provides an interactive command-line interface:

- **'s'** - Display buffer status
- **'t'** - Test basic commands (S001, S002, U001)
- **'q'** - Quit application

### API Usage Examples

#### System Configuration (S-series)
```cpp
RS485Driver driver;
driver.Initialize();

// Set slave address to 5
driver.configureSystemSettings("S001", 5);

// Set baud rate to 115200
driver.configureSystemSettings("S002", 115200);
```

#### User Configuration (U-series)
```cpp
// Set SEL detection threshold to 250mA
driver.configureUserSettings("U001", 250);

// Set maximum amplitude threshold to 1500mA
driver.configureUserSettings("U002", 1500);

// Enable GPIO input channel 0
uint64_t gpio_value = RS485DataFormat::encodeDualIntegers(0, 1); // Channel=0, Enable=1
driver.configureUserSettings("U005", gpio_value);
```

#### Data Requests (A-series)
```cpp
std::vector<uint8_t> responseData;

// Request SEL event log
RS485Error result = driver.requestData(5, "A001", responseData);
if (result == RS485Error::SUCCESS) {
    // Process response data
}
```

## Supported Commands

### S-series (System Configuration)
- **S001**: Set RS485 slave address (1-31)
- **S002**: Set baud rate (9600, 19200, 38400, 57600, 115200)

### U-series (User Configuration)
- **U001**: Set SEL detection threshold (40-500 mA)
- **U002**: Set SEL maximum amplitude threshold (1000-2000 mA)
- **U003**: Set number of SEL detections before power cycle (1-5)
- **U004**: Set power cycle duration (200, 400, 600, 800, 1000 ms)
- **U005**: Enable/disable GPIO input functions
- **U006**: Enable/disable GPIO output functions

### A-series (Application Data)
- **A001**: Request SEL event log
- **A002**: Request device status
- **A003**: Request firmware version
- **A004**: Request system statistics
- **A005**: Request current configuration

### W-series (AI Model Data)
- **W001**: Write model data to FRAM
- **W002**: Read model data from FRAM

## Protocol Details

### Frame Format (16 bytes)
```
| Header | ID Byte | Payload (12 bytes) | CRC8 | Trailer |
|  0xAA  | 1 byte  | Key(4) + Value(8)  | 1 byte | 0x0D  |
```

### Function Codes
- **0b111**: Assign data (Master use)
- **0b110**: Request data (Master use)
- **0b010**: Response to Assign (Slave use)
- **0b001**: Response to Request (Slave use)
- **0b000**: Re-send request (Both use)

## Troubleshooting

### Common Issues

1. **"Administrator Required" Error**
   - Right-click the executable and select "Run as administrator"
   - Driver installation requires elevated privileges

2. **"Device Not Found" Error**
   - Check USB-RS485 converter connection
   - Verify device appears in Device Manager
   - Try a different USB port

3. **"Communication Timeout" Error**
   - Check RS485 cable connections
   - Verify slave device is powered and responding
   - Check baud rate settings match between master and slave

4. **Build Errors**
   - Ensure Visual Studio C++ tools are installed
   - Verify CMake is in PATH
   - Check that all required files are present

### Debug Mode

To enable debug output, build with:
```cmd
cmake --build . --config Debug
```

## File Structure

```
RS485_driver_development/
├── src/                          # Source code
│   ├── main.cpp                  # Main application entry point
│   ├── RS485Driver.cpp           # RS485 driver implementation
│   └── FTDIInstaller.cpp         # FTDI driver installation
├── include/                      # Header files
│   ├── RS485Types.h              # Type definitions
│   ├── RS485DataFormat.h         # Data format utilities
│   ├── CRC8Calculator.h          # CRC calculation
│   ├── RS485Driver.h             # Main driver interface
│   ├── FTDIInstaller.h           # FTDI installer interface
│   └── resource.h                # Resource definitions
├── resources/                    # Application resources
│   ├── application.rc            # Resource script
│   ├── application.manifest      # Windows manifest
│   └── app_icon.ico              # Application icon
├── build/                        # Build output directory
├── CMakeLists.txt                # CMake configuration
├── build.bat                     # Build script
└── README.md                     # This file
```

## License

Copyright (C) 2024. All rights reserved.

## Support

For technical support or questions, please contact the development team.
