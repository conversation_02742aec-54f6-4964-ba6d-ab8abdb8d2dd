@echo off
echo ========================================
echo    RS485 Communication Demo
echo      Simple Build Script
echo ========================================
echo.

if not exist build mkdir build
cd build

echo Compiling RS485 Demo...

REM Try MinGW first (usually available)
g++.exe --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW G++ Compiler
    g++ -std=c++17 -O2 -Wall ../src/SimpleRS485Demo.cpp -o RS485_Demo.exe
    goto :check_result
)

REM Try MSVC
cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using Microsoft Visual C++ Compiler
    cl /EHsc /std:c++17 /O2 ../src/SimpleRS485Demo.cpp /Fe:RS485_Demo.exe
    goto :check_result
)

echo ERROR: No C++ compiler found!
echo Please install MinGW or Visual Studio.
pause
exit /b 1

:check_result
if exist RS485_Demo.exe (
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo.
    echo Output: build\RS485_Demo.exe
    echo.
    echo This demo shows:
    echo - ZES protocol frame structure
    echo - S001, S002, U001, A001 command generation
    echo - CRC8 calculation
    echo - Frame parsing and display
    echo.
    echo Run the demo to see RS485 frame generation!
    echo ========================================
    echo.
    echo Starting demo...
    echo.
    RS485_Demo.exe
) else (
    echo.
    echo ========================================
    echo Build FAILED!
    echo Check the error messages above.
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
