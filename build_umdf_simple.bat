@echo off
echo ========================================
echo   UMDF Driver Simple Build
echo ========================================
echo.

REM Create build directories
if not exist build mkdir build
if not exist build\driver mkdir build\driver

REM Check for compiler
where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Setting up Visual Studio environment...
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo ERROR: Visual Studio not found!
        pause
        exit /b 1
    )
)

echo Compiler ready: 
cl 2>&1 | find "Microsoft"

echo.
echo ========================================
echo Building Simplified UMDF Driver
echo ========================================
echo.

REM First, let's create a simplified driver that compiles
echo Creating simplified driver source...

cd driver

echo Compiling simplified UMDF driver...
cl /c /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
   /I..\include ^
   RS485FilterDriver.cpp dllsup.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    cd ..
    pause
    exit /b 1
)

echo Linking driver DLL...
link /DLL /DEF:RS485Filter.def /OUT:..\build\driver\RS485Filter.dll ^
     RS485FilterDriver.obj dllsup.obj ^
     kernel32.lib user32.lib setupapi.lib advapi32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo Build Results
echo ========================================
echo.

if exist build\driver\RS485Filter.dll (
    echo ✓ Driver DLL: build\driver\RS485Filter.dll
    dir build\driver\RS485Filter.dll | find "RS485Filter.dll"
    
    echo ✓ Copying INF file...
    copy driver\RS485Filter.inf build\driver\
    
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo ========================================
    echo.
    echo Files created:
    echo - build\driver\RS485Filter.dll
    echo - build\driver\RS485Filter.inf
    echo.
    echo This is a simplified UMDF driver for testing.
    echo For production use, build with full WDK environment.
    echo.
    echo Next steps:
    echo 1. Test the driver installation
    echo 2. Run the user application
    echo 3. Verify communication
    
) else (
    echo ✗ Build failed - DLL not created
    echo Check error messages above
)

echo.
echo Press any key to exit...
pause >nul
