#ifndef RS485_DATA_FORMAT_H
#define RS485_DATA_FORMAT_H

#include "RS485Types.h"
#include <cstring>

/**
 * @brief Helper class for RS485 data format conversion
 * 
 * This class provides utility functions to convert between different data types
 * and the standardized 8-byte payload format used in RS485 communication.
 * All multi-byte values use little-endian format for cross-platform compatibility.
 */
class RS485DataFormat {
public:
    /**
     * @brief Convert single 32-bit integer to payload format
     * @param value The integer value to encode
     * @return 64-bit payload with value in lower 4 bytes, upper 4 bytes set to zero
     */
    static uint64_t encodeInteger(uint32_t value) {
        return static_cast<uint64_t>(value);
    }

    /**
     * @brief Convert dual 32-bit integers to payload format
     * @param value1 First integer (stored in bytes 0-3)
     * @param value2 Second integer (stored in bytes 4-7)
     * @return 64-bit payload containing both values
     */
    static uint64_t encodeDualIntegers(uint32_t value1, uint32_t value2) {
        return static_cast<uint64_t>(value1) | (static_cast<uint64_t>(value2) << 32);
    }

    /**
     * @brief Convert float to payload format (future support)
     * @param value IEEE 754 single-precision float
     * @return 64-bit payload with float in lower 4 bytes
     */
    static uint64_t encodeFloat(float value) {
        uint32_t bits = *reinterpret_cast<uint32_t*>(&value);
        return static_cast<uint64_t>(bits);
    }

    /**
     * @brief Convert double to payload format (future support)
     * @param value IEEE 754 double-precision float
     * @return 64-bit payload containing the double value
     */
    static uint64_t encodeDouble(double value) {
        return *reinterpret_cast<uint64_t*>(&value);
    }

    /**
     * @brief Decode single integer from payload
     * @param payload 64-bit payload data
     * @return 32-bit integer from lower 4 bytes
     */
    static uint32_t decodeInteger(uint64_t payload) {
        return static_cast<uint32_t>(payload & 0xFFFFFFFF);
    }

    /**
     * @brief Decode dual integers from payload
     * @param payload 64-bit payload data
     * @return Pair containing first integer (bytes 0-3) and second integer (bytes 4-7)
     */
    static std::pair<uint32_t, uint32_t> decodeDualIntegers(uint64_t payload) {
        uint32_t value1 = static_cast<uint32_t>(payload & 0xFFFFFFFF);
        uint32_t value2 = static_cast<uint32_t>((payload >> 32) & 0xFFFFFFFF);
        return std::make_pair(value1, value2);
    }

    /**
     * @brief Decode float from payload (future support)
     * @param payload 64-bit payload data
     * @return IEEE 754 single-precision float from lower 4 bytes
     */
    static float decodeFloat(uint64_t payload) {
        uint32_t bits = static_cast<uint32_t>(payload & 0xFFFFFFFF);
        return *reinterpret_cast<float*>(&bits);
    }

    /**
     * @brief Decode double from payload (future support)
     * @param payload 64-bit payload data
     * @return IEEE 754 double-precision float
     */
    static double decodeDouble(uint64_t payload) {
        return *reinterpret_cast<double*>(&payload);
    }

    /**
     * @brief Create payload from command key and value
     * @param commandKey 4-byte ASCII command identifier (e.g., "S001", "U001")
     * @param value 8-byte binary value
     * @param payload Output buffer for 12-byte payload
     */
    static void createPayload(const char* commandKey, uint64_t value, uint8_t* payload) {
        // Copy command key (4 bytes)
        std::memcpy(payload, commandKey, RS485_KEY_SIZE);
        
        // Copy value in little-endian format (8 bytes)
        for (size_t i = 0; i < RS485_VALUE_SIZE; ++i) {
            payload[RS485_KEY_SIZE + i] = static_cast<uint8_t>((value >> (i * 8)) & 0xFF);
        }
    }

    /**
     * @brief Extract command key and value from payload
     * @param payload 12-byte payload data
     * @param commandKey Output buffer for 4-byte command key (null-terminated)
     * @param value Output for 8-byte value
     */
    static void parsePayload(const uint8_t* payload, char* commandKey, uint64_t* value) {
        // Extract command key (4 bytes)
        std::memcpy(commandKey, payload, RS485_KEY_SIZE);
        commandKey[RS485_KEY_SIZE] = '\0'; // Null-terminate
        
        // Extract value in little-endian format (8 bytes)
        *value = 0;
        for (size_t i = 0; i < RS485_VALUE_SIZE; ++i) {
            *value |= static_cast<uint64_t>(payload[RS485_KEY_SIZE + i]) << (i * 8);
        }
    }

    /**
     * @brief Validate command key format
     * @param commandKey Command key to validate
     * @return true if valid format (4 ASCII characters)
     */
    static bool isValidCommandKey(const char* commandKey) {
        if (!commandKey) return false;
        
        // Check length
        size_t len = std::strlen(commandKey);
        if (len != RS485_KEY_SIZE) return false;
        
        // Check ASCII characters
        for (size_t i = 0; i < RS485_KEY_SIZE; ++i) {
            if (commandKey[i] < 0x20 || commandKey[i] > 0x7E) {
                return false; // Non-printable ASCII
            }
        }
        
        return true;
    }

    /**
     * @brief Convert bytes to hex string for debugging
     * @param data Byte array
     * @param length Number of bytes
     * @return Hex string representation
     */
    static std::string bytesToHexString(const uint8_t* data, size_t length) {
        std::string result;
        result.reserve(length * 3);
        
        for (size_t i = 0; i < length; ++i) {
            char hex[4];
            sprintf_s(hex, sizeof(hex), "%02X ", data[i]);
            result += hex;
        }
        
        if (!result.empty()) {
            result.pop_back(); // Remove trailing space
        }
        
        return result;
    }
};

#endif // RS485_DATA_FORMAT_H
