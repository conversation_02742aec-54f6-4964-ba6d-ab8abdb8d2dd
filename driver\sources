#
# RS485 Filter Driver - WDK Build Configuration
#

TARGETNAME=RS485Filter
TARGETTYPE=DYNLINK
DLLDEF=RS485Filter.def

# Target platform
_NT_TARGET_VERSION=$(_NT_TARGET_VERSION_WIN7)
KMDF_VERSION_MAJOR=1
UMDF_VERSION_MAJOR=2

# Include directories
INCLUDES=$(INCLUDES); \
         ..\include; \
         $(DDK_INC_PATH); \
         $(SDK_INC_PATH)

# Source files
SOURCES=RS485FilterDriver.cpp

# Libraries
TARGETLIBS=$(TARGETLIBS) \
           $(SDK_LIB_PATH)\kernel32.lib \
           $(SDK_LIB_PATH)\user32.lib \
           $(SDK_LIB_PATH)\setupapi.lib \
           $(SDK_LIB_PATH)\cfgmgr32.lib \
           $(DDK_LIB_PATH)\wdf01000.lib

# Compiler options
C_DEFINES=$(C_DEFINES) -DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN

# UMDF specific settings
UMDF_VERSION_MAJOR=2
UMDF_VERSION_MINOR=0

# Driver type
DRIVER_TYPE=WDM

# Entry point
DLLENTRY=_DllMainCRTStartup

# Use C++ runtime
USE_MSVCRT=1
USE_STL=1

# Warning level
MSC_WARNING_LEVEL=/W4

# Optimization
MSC_OPTIMIZATION=/O2

# Debug information
USE_PDB=1
