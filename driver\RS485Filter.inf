;
; RS485Filter.inf - RS485 Communication Filter Driver
;
; This INF file installs the RS485 filter driver that sits above the FTDI VCP driver
; and provides ZES protocol processing and advanced buffer management.
;

[Version]
Signature="$WINDOWS NT$"
Class=System
ClassGuid={4d36e97d-e325-11ce-bfc1-08002be10318}
Provider=%ManufacturerName%
CatalogFile=RS485Filter.cat
DriverVer=01/01/2024,1.0.0.0
PnpLockdown=1

[DestinationDirs]
DefaultDestDir = 12
RS485Filter_Device_CoInstaller_CopyFiles = 11

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
RS485Filter.dll=1,,
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll=1

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%RS485Filter.DeviceDesc%=RS485Filter_Device, USB\VID_0403&PID_6001

[RS485Filter_Device.NT]
CopyFiles=Drivers_Dir

[Drivers_Dir]
RS485Filter.dll

[RS485Filter_Device.NT.HW]
AddReg=RS485Filter_AddReg

[RS485Filter_AddReg]
; Add filter driver to upper filters
HKR,,"UpperFilters",0x00010000,"RS485Filter"

[RS485Filter_Device.NT.Services]
AddService = RS485Filter,%SPSVCINST_ASSOCSERVICE%, RS485Filter_Service_Inst
AddService = WUDFRd,0x000001fa, WUDFRD_ServiceInstall

[RS485Filter_Service_Inst]
DisplayName = %RS485Filter.SVCDESC%
ServiceType = 1               ; SERVICE_KERNEL_DRIVER
StartType = 3                 ; SERVICE_DEMAND_START
ErrorControl = 1              ; SERVICE_ERROR_NORMAL
ServiceBinary = %12%\RS485Filter.dll

[WUDFRD_ServiceInstall]
DisplayName = %WudfRdDisplayName%
ServiceType = 1
StartType = 3
ErrorControl = 1
ServiceBinary = %12%\WUDFRd.sys

[RS485Filter_Device.NT.CoInstallers]
AddReg=RS485Filter_Device_CoInstaller_AddReg
CopyFiles=RS485Filter_Device_CoInstaller_CopyFiles

[RS485Filter_Device_CoInstaller_CopyFiles]
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll

[RS485Filter_Device_CoInstaller_AddReg]
HKR,,CoInstallers32,0x00010000, "WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll,WdfCoInstaller"

[RS485Filter_Device.NT.Wdf]
UmdfService=RS485Filter,RS485Filter_Install
UmdfServiceOrder=RS485Filter

[RS485Filter_Install]
UmdfLibraryVersion=$UMDFVERSION$
ServiceBinary=%12%\UMDF\RS485Filter.dll

[Strings]
SPSVCINST_ASSOCSERVICE= 0x00000002
ManufacturerName="Your Company"
DiskName = "RS485 Filter Driver Installation Disk"
RS485Filter.DeviceDesc = "RS485 Communication Filter Driver"
RS485Filter.SVCDESC = "RS485 Communication Filter Driver"
WudfRdDisplayName="Windows Driver Foundation - User-mode Driver Framework Reflector"
