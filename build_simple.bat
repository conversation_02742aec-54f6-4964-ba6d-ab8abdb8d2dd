@echo off
echo ========================================
echo    RS485 Communication Application
echo      Quick Build (No WDK Required)
echo ========================================
echo.

REM Create a simple console application first
echo Creating simple console version...

if not exist build mkdir build
cd build

echo Compiling with available compiler...

REM Try different compiler options
set COMPILER_FOUND=0

REM Check for MSVC
cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using Microsoft Visual C++ Compiler
    set COMPILER_FOUND=1
    goto :compile_msvc
)

REM Check for MinGW
g++.exe --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW G++ Compiler
    set COMPILER_FOUND=1
    goto :compile_mingw
)

if %COMPILER_FOUND% EQU 0 (
    echo ERROR: No suitable C++ compiler found!
    echo.
    echo Please install one of the following:
    echo 1. Visual Studio 2019/2022 with C++ tools
    echo 2. MinGW-w64
    echo 3. Build Tools for Visual Studio
    echo.
    pause
    exit /b 1
)

:compile_msvc
echo Compiling with MSVC...
cl /EHsc /std:c++17 /DWIN32 /D_WINDOWS /I..\include ^
   ..\src\main.cpp ..\src\RS485Driver.cpp ..\src\FTDIInstaller.cpp ^
   /Fe:RS485_Communication_Application.exe ^
   setupapi.lib newdev.lib advapi32.lib user32.lib kernel32.lib version.lib cfgmgr32.lib
goto :check_result

:compile_mingw
echo Compiling with MinGW...
g++ -std=c++17 -DWIN32 -D_WINDOWS -I../include ^
    ../src/main.cpp ../src/RS485Driver.cpp ../src/FTDIInstaller.cpp ^
    -o RS485_Communication_Application.exe ^
    -lsetupapi -lnewdev -ladvapi32 -luser32 -lkernel32 -lversion -lcfgmgr32
goto :check_result

:check_result
if exist RS485_Communication_Application.exe (
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo.
    echo Output: build\RS485_Communication_Application.exe
    echo.
    echo This is a simplified version that demonstrates:
    echo - FTDI driver installation
    echo - Basic RS485 communication structure
    echo - S001 and S002 command framework
    echo.
    echo For full UMDF driver implementation, see:
    echo UMDF_Implementation_Guide.md
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Build FAILED!
    echo.
    echo Check the error messages above.
    echo You may need to install additional dependencies.
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
