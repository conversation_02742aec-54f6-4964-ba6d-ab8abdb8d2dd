#ifndef CRC8_CALCULATOR_H
#define CRC8_CALCULATOR_H

#include "RS485Types.h"

/**
 * @brief CRC8 Calculator for RS485 Frame Validation
 * 
 * Implements CRC8 calculation using polynomial 0x97 (x^8 + x^5 + x^3 + x^2 + x + 1)
 * as specified in the ZES protocol documentation.
 */
class CRC8Calculator {
private:
    static uint8_t crc8_table[256];
    static bool table_initialized;

    /**
     * @brief Initialize CRC8 lookup table
     */
    static void initializeTable() {
        if (table_initialized) return;

        for (uint16_t i = 0; i < 256; ++i) {
            uint8_t crc = static_cast<uint8_t>(i);
            
            for (uint8_t j = 0; j < 8; ++j) {
                if (crc & 0x80) {
                    crc = (crc << 1) ^ CRC8_POLYNOMIAL;
                } else {
                    crc <<= 1;
                }
            }
            
            crc8_table[i] = crc;
        }
        
        table_initialized = true;
    }

public:
    /**
     * @brief Calculate CRC8 for a data buffer
     * @param data Pointer to data buffer
     * @param length Number of bytes to process
     * @param initial_value Initial CRC value (default: 0x00)
     * @return Calculated CRC8 value
     */
    static uint8_t calculate(const uint8_t* data, size_t length, uint8_t initial_value = 0x00) {
        if (!table_initialized) {
            initializeTable();
        }

        uint8_t crc = initial_value;
        
        for (size_t i = 0; i < length; ++i) {
            crc = crc8_table[crc ^ data[i]];
        }
        
        return crc;
    }

    /**
     * @brief Calculate CRC8 for RS485 frame (ID byte + 12-byte payload)
     * @param frame Pointer to RS485 frame structure
     * @return Calculated CRC8 value
     */
    static uint8_t calculateFrameCRC(const RS485Frame* frame) {
        if (!frame) return 0;

        // CRC covers ID byte + 12-byte payload (13 bytes total)
        const uint8_t* data_start = &frame->id_byte;
        constexpr size_t crc_data_length = 1 + RS485_PAYLOAD_SIZE; // ID + payload
        
        return calculate(data_start, crc_data_length);
    }

    /**
     * @brief Validate CRC8 for RS485 frame
     * @param frame Pointer to RS485 frame structure
     * @return true if CRC is valid, false otherwise
     */
    static bool validateFrameCRC(const RS485Frame* frame) {
        if (!frame) return false;

        uint8_t calculated_crc = calculateFrameCRC(frame);
        return calculated_crc == frame->crc8;
    }

    /**
     * @brief Calculate CRC8 for payload data
     * @param id_byte Frame ID byte (function code + device address)
     * @param payload Pointer to 12-byte payload
     * @return Calculated CRC8 value
     */
    static uint8_t calculatePayloadCRC(uint8_t id_byte, const uint8_t* payload) {
        if (!payload) return 0;

        if (!table_initialized) {
            initializeTable();
        }

        uint8_t crc = 0x00;
        
        // Process ID byte
        crc = crc8_table[crc ^ id_byte];
        
        // Process 12-byte payload
        for (size_t i = 0; i < RS485_PAYLOAD_SIZE; ++i) {
            crc = crc8_table[crc ^ payload[i]];
        }
        
        return crc;
    }

    /**
     * @brief Update CRC8 with additional byte
     * @param current_crc Current CRC value
     * @param data_byte New byte to include in CRC
     * @return Updated CRC8 value
     */
    static uint8_t updateCRC(uint8_t current_crc, uint8_t data_byte) {
        if (!table_initialized) {
            initializeTable();
        }

        return crc8_table[current_crc ^ data_byte];
    }

    /**
     * @brief Reset CRC calculation state
     */
    static void reset() {
        // CRC8 is stateless, but this function is provided for API consistency
        // In case future implementations need state management
    }

    /**
     * @brief Get CRC8 polynomial value
     * @return CRC8 polynomial (0x97)
     */
    static uint8_t getPolynomial() {
        return CRC8_POLYNOMIAL;
    }

    /**
     * @brief Test CRC8 calculation with known test vectors
     * @return true if all tests pass, false otherwise
     */
    static bool selfTest() {
        // Test vector 1: Empty data
        uint8_t empty_data[] = {};
        uint8_t crc1 = calculate(empty_data, 0);
        if (crc1 != 0x00) return false;

        // Test vector 2: Single byte
        uint8_t single_byte[] = { 0xE1 };
        uint8_t crc2 = calculate(single_byte, 1);
        // Expected CRC for 0xE1 with polynomial 0x97 should be calculated

        // Test vector 3: Example frame data
        uint8_t test_data[] = { 0xE1, 0x53, 0x30, 0x30, 0x31, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00 };
        uint8_t crc3 = calculate(test_data, sizeof(test_data));
        
        // For now, just verify the calculation doesn't crash
        // In production, you would verify against known good CRC values
        return true;
    }
};

// Static member definitions
uint8_t CRC8Calculator::crc8_table[256];
bool CRC8Calculator::table_initialized = false;

#endif // CRC8_CALCULATOR_H
