@echo off
echo ========================================
echo    RS485 UMDF Driver Build Script
echo ========================================
echo.

REM Check for WDK installation
set WDK_PATH=
if exist "C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\msbuild.exe" (
    set WDK_PATH=C:\Program Files (x86)\Windows Kits\10
    set WDK_VERSION=10.0.22621.0
) else if exist "C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\x64\msbuild.exe" (
    set WDK_PATH=C:\Program Files (x86)\Windows Kits\10
    set WDK_VERSION=10.0.19041.0
) else if exist "C:\Program Files (x86)\Windows Kits\10\bin\10.0.18362.0\x64\msbuild.exe" (
    set WDK_PATH=C:\Program Files (x86)\Windows Kits\10
    set WDK_VERSION=10.0.18362.0
) else (
    echo ERROR: Windows Driver Kit (WDK) not found!
    echo.
    echo Please install WDK 10 from:
    echo https://docs.microsoft.com/en-us/windows-hardware/drivers/download-the-wdk
    echo.
    pause
    exit /b 1
)

echo Found WDK at: %WDK_PATH%
echo WDK Version: %WDK_VERSION%
echo.

REM Set up WDK environment
set PATH=%WDK_PATH%\bin\%WDK_VERSION%\x64;%PATH%
set INCLUDE=%WDK_PATH%\Include\%WDK_VERSION%\um;%WDK_PATH%\Include\%WDK_VERSION%\shared;%WDK_PATH%\Include\%WDK_VERSION%\km;%INCLUDE%
set LIB=%WDK_PATH%\Lib\%WDK_VERSION%\um\x64;%WDK_PATH%\Lib\%WDK_VERSION%\km\x64;%LIB%

REM Check for Visual Studio
call "%ProgramFiles%\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if %ERRORLEVEL% NEQ 0 (
    call "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Visual Studio not found!
        echo Please install Visual Studio 2019 or 2022 with C++ development tools.
        pause
        exit /b 1
    )
)

echo Visual Studio environment configured.
echo.

REM Create build directories
if not exist build mkdir build
if not exist build\driver mkdir build\driver
if not exist build\application mkdir build\application

echo ========================================
echo Building UMDF Driver
echo ========================================
echo.

REM Build the UMDF driver
cd driver

echo Compiling RS485FilterDriver.cpp...
cl /c /EHsc /std:c++17 /DUNICODE /D_UNICODE /DWIN32_LEAN_AND_MEAN ^
   /I..\include /I"%WDK_PATH%\Include\%WDK_VERSION%\um" ^
   /I"%WDK_PATH%\Include\%WDK_VERSION%\shared" ^
   /I"%WDK_PATH%\Include\%WDK_VERSION%\km" ^
   /Fo..\build\driver\ RS485FilterDriver.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Driver compilation failed!
    cd ..
    pause
    exit /b 1
)

echo Linking RS485Filter.dll...
link /DLL /DEF:RS485Filter.def /OUT:..\build\driver\RS485Filter.dll ^
     /LIBPATH:"%WDK_PATH%\Lib\%WDK_VERSION%\um\x64" ^
     /LIBPATH:"%WDK_PATH%\Lib\%WDK_VERSION%\km\x64" ^
     ..\build\driver\RS485FilterDriver.obj ^
     kernel32.lib user32.lib setupapi.lib cfgmgr32.lib wdf01000.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Driver linking failed!
    cd ..
    pause
    exit /b 1
)

cd ..

echo ✓ UMDF Driver built successfully!
echo.

echo ========================================
echo Building User Application
echo ========================================
echo.

REM Build the user application
echo Compiling user application...
cl /EHsc /std:c++17 /DUNICODE /D_UNICODE /DWIN32_LEAN_AND_MEAN ^
   /I include src\UMDFApplication.cpp src\RS485DriverInterface.cpp src\FTDIInstaller.cpp ^
   /Fe:build\application\RS485_UMDF_Application.exe ^
   setupapi.lib newdev.lib advapi32.lib user32.lib kernel32.lib version.lib cfgmgr32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Application compilation failed!
    pause
    exit /b 1
)

echo ✓ User Application built successfully!
echo.

echo ========================================
echo Build Summary
echo ========================================
echo.
echo UMDF Driver: build\driver\RS485Filter.dll
echo User App:    build\application\RS485_UMDF_Application.exe
echo INF File:    driver\RS485Filter.inf
echo.
echo Next steps:
echo 1. Sign the driver (for production deployment)
echo 2. Install the driver using the INF file
echo 3. Run the user application to test communication
echo.
echo For development/testing:
echo - Use test signing: bcdedit /set testsigning on
echo - Install driver: pnputil /add-driver driver\RS485Filter.inf /install
echo.

if exist build\driver\RS485Filter.dll (
    echo Driver file size:
    dir build\driver\RS485Filter.dll | find "RS485Filter.dll"
)

if exist build\application\RS485_UMDF_Application.exe (
    echo Application file size:
    dir build\application\RS485_UMDF_Application.exe | find "RS485_UMDF_Application.exe"
)

echo.
echo Press any key to exit...
pause >nul
