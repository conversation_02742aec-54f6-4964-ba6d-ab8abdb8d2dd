#include "RS485DriverInterface.h"
#include "RS485DataFormat.h"
#include <setupapi.h>
#include <iostream>
#include <sstream>

#pragma comment(lib, "setupapi.lib")

RS485DriverInterface::RS485DriverInterface() :
    m_hDriver(INVALID_HANDLE_VALUE),
    m_currentSlaveAddress(0),
    m_isOpen(false)
{
}

RS485DriverInterface::~RS485DriverInterface()
{
    closeDriver();
}

RS485Error RS485DriverInterface::openDriver(const std::wstring& devicePath)
{
    if (m_isOpen) {
        return RS485Error::DEVICE_ALREADY_OPEN;
    }

    std::wstring driverPath = devicePath;
    if (driverPath.empty()) {
        driverPath = findDriverDevicePath();
        if (driverPath.empty()) {
            return RS485Error::DEVICE_NOT_FOUND;
        }
    }

    // Open handle to driver
    m_hDriver = CreateFileW(
        driverPath.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr
    );

    if (m_hDriver == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        switch (error) {
            case ERROR_FILE_NOT_FOUND:
            case ERROR_PATH_NOT_FOUND:
                return RS485Error::DEVICE_NOT_FOUND;
            case ERROR_ACCESS_DENIED:
                return RS485Error::INSUFFICIENT_PRIVILEGES;
            default:
                return RS485Error::DRIVER_ERROR;
        }
    }

    m_devicePath = driverPath;
    m_isOpen = true;

    return RS485Error::SUCCESS;
}

RS485Error RS485DriverInterface::closeDriver()
{
    if (m_hDriver != INVALID_HANDLE_VALUE) {
        CloseHandle(m_hDriver);
        m_hDriver = INVALID_HANDLE_VALUE;
    }

    m_isOpen = false;
    m_devicePath.clear();

    return RS485Error::SUCCESS;
}

bool RS485DriverInterface::isDriverOpen() const
{
    return m_isOpen && (m_hDriver != INVALID_HANDLE_VALUE);
}

RS485Error RS485DriverInterface::getBufferStatus(BufferStatus& status)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    DWORD bytesReturned;
    return sendDeviceIoControl(
        IOCTL_RS485_GET_BUFFER_STATUS,
        nullptr, 0,
        &status, sizeof(status),
        &bytesReturned
    );
}

RS485Error RS485DriverInterface::clearBuffer(BufferType bufferType)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    return sendDeviceIoControl(
        IOCTL_RS485_CLEAR_BUFFER,
        &bufferType, sizeof(bufferType),
        nullptr, 0
    );
}

RS485Error RS485DriverInterface::getHardwareStatus(HardwareStatus& status)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    DWORD bytesReturned;
    return sendDeviceIoControl(
        IOCTL_RS485_GET_HARDWARE_STATUS,
        nullptr, 0,
        &status, sizeof(status),
        &bytesReturned
    );
}

RS485Error RS485DriverInterface::getPerformanceMetrics(PerformanceMetrics& metrics)
{
    // This would be implemented with a separate IOCTL
    // For now, return placeholder data
    std::memset(&metrics, 0, sizeof(metrics));
    return RS485Error::SUCCESS;
}

const char* RS485DriverInterface::getErrorString(RS485Error error) const
{
    switch (error) {
        case RS485Error::SUCCESS: return "Success";
        case RS485Error::DEVICE_NOT_FOUND: return "Device not found";
        case RS485Error::DEVICE_ALREADY_OPEN: return "Device already open";
        case RS485Error::DEVICE_NOT_OPEN: return "Device not open";
        case RS485Error::INVALID_PARAMETER: return "Invalid parameter";
        case RS485Error::INSUFFICIENT_BUFFER: return "Insufficient buffer space";
        case RS485Error::COMMUNICATION_TIMEOUT: return "Communication timeout";
        case RS485Error::CRC_ERROR: return "CRC error";
        case RS485Error::FRAME_ERROR: return "Frame error";
        case RS485Error::BUFFER_OVERFLOW: return "Buffer overflow";
        case RS485Error::DRIVER_ERROR: return "Driver error";
        case RS485Error::INSUFFICIENT_PRIVILEGES: return "Insufficient privileges";
        case RS485Error::INSTALLATION_FAILED: return "Installation failed";
        case RS485Error::INVALID_FUNCTION_CODE: return "Invalid function code";
        case RS485Error::INVALID_DEVICE_ADDRESS: return "Invalid device address";
        case RS485Error::HARDWARE_ERROR: return "Hardware error";
        default: return "Unknown error";
    }
}

RS485Error RS485DriverInterface::configureSystemSettings(const std::string& commandKey, uint64_t value)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    RS485Error result = validateCommandKey(commandKey);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    if (commandKey[0] != 'S') {
        return RS485Error::INVALID_PARAMETER;
    }

    RS485_COMMAND_INPUT input = {};
    strncpy_s(input.CommandKey, sizeof(input.CommandKey), commandKey.c_str(), 4);
    input.Value = value;
    input.SlaveAddress = 0; // Broadcast for S-series

    // Special handling for S001 (slave address assignment)
    if (commandKey == "S001") {
        uint32_t address = RS485DataFormat::decodeInteger(value);
        if (address < 1 || address > 31) {
            return RS485Error::INVALID_DEVICE_ADDRESS;
        }
        m_currentSlaveAddress = static_cast<uint8_t>(address);
    }

    return sendDeviceIoControl(
        IOCTL_RS485_CONFIGURE_SYSTEM,
        &input, sizeof(input),
        nullptr, 0
    );
}

RS485Error RS485DriverInterface::configureUserSettings(const std::string& commandKey, uint64_t value)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    RS485Error result = validateCommandKey(commandKey);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    if (commandKey[0] != 'U') {
        return RS485Error::INVALID_PARAMETER;
    }

    if (m_currentSlaveAddress == 0) {
        return RS485Error::INVALID_DEVICE_ADDRESS;
    }

    RS485_COMMAND_INPUT input = {};
    strncpy_s(input.CommandKey, sizeof(input.CommandKey), commandKey.c_str(), 4);
    input.Value = value;
    input.SlaveAddress = m_currentSlaveAddress;

    return sendDeviceIoControl(
        IOCTL_RS485_CONFIGURE_USER,
        &input, sizeof(input),
        nullptr, 0
    );
}

RS485Error RS485DriverInterface::modelDataOperation(uint8_t slaveAddress, uint32_t address, 
                                                   uint8_t* data, bool isWrite, size_t length)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    RS485Error result = validateSlaveAddress(slaveAddress);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    if (!data || length == 0 || length > 256) {
        return RS485Error::INVALID_PARAMETER;
    }

    RS485_MODEL_DATA_INPUT input = {};
    input.SlaveAddress = slaveAddress;
    input.Address = address;
    input.Length = static_cast<uint32_t>(length);
    input.IsWrite = isWrite ? TRUE : FALSE;
    
    if (isWrite) {
        std::memcpy(input.Data, data, length);
    }

    DWORD bytesReturned;
    result = sendDeviceIoControl(
        IOCTL_RS485_MODEL_DATA_OP,
        &input, sizeof(input),
        &input, sizeof(input), // Use same buffer for output
        &bytesReturned
    );

    if (result == RS485Error::SUCCESS && !isWrite) {
        // Copy read data back
        std::memcpy(data, input.Data, length);
    }

    return result;
}

RS485Error RS485DriverInterface::requestData(uint8_t slaveAddress, const std::string& dataKey,
                                            std::vector<uint8_t>& responseData, uint32_t timeout)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    RS485Error result = validateSlaveAddress(slaveAddress);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    result = validateCommandKey(dataKey);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    if (dataKey[0] != 'A') {
        return RS485Error::INVALID_PARAMETER;
    }

    // Send request
    RS485_REQUEST_INPUT input = {};
    strncpy_s(input.DataKey, sizeof(input.DataKey), dataKey.c_str(), 4);
    input.SlaveAddress = slaveAddress;
    input.Timeout = timeout;

    result = sendDeviceIoControl(
        IOCTL_RS485_REQUEST_DATA,
        &input, sizeof(input),
        nullptr, 0
    );

    if (result != RS485Error::SUCCESS) {
        return result;
    }

    // Wait for response
    return receiveSlaveResponse(slaveAddress, responseData, timeout);
}

RS485Error RS485DriverInterface::receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                                     uint32_t timeout)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    UNREFERENCED_PARAMETER(slaveAddress); // Would be used for filtering
    UNREFERENCED_PARAMETER(timeout);      // Would be used for timeout handling

    responseData.clear();

    RS485_RESPONSE_OUTPUT output = {};
    DWORD bytesReturned;

    RS485Error result = sendDeviceIoControl(
        IOCTL_RS485_RECEIVE_RESPONSE,
        nullptr, 0,
        &output, sizeof(output),
        &bytesReturned
    );

    if (result == RS485Error::SUCCESS) {
        responseData.assign(output.PayloadData, output.PayloadData + output.DataLength);
    }

    return result;
}

RS485Error RS485DriverInterface::Initialize()
{
    // Find and open driver
    std::wstring driverPath = findDriverDevicePath();
    if (driverPath.empty()) {
        return RS485Error::DEVICE_NOT_FOUND;
    }

    return openDriver(driverPath);
}

void RS485DriverInterface::setCurrentSlaveAddress(uint8_t address)
{
    if (address >= 1 && address <= 31) {
        m_currentSlaveAddress = address;
    }
}

uint8_t RS485DriverInterface::getCurrentSlaveAddress() const
{
    return m_currentSlaveAddress;
}

std::wstring RS485DriverInterface::findDriverDevicePath()
{
    // Get device information set for our driver class
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
        nullptr,
        L"RS485Filter",
        nullptr,
        DIGCF_PRESENT | DIGCF_DEVICEINTERFACE
    );

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        return L"";
    }

    SP_DEVICE_INTERFACE_DATA deviceInterfaceData = {};
    deviceInterfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);

    // Enumerate device interfaces
    if (SetupDiEnumDeviceInterfaces(deviceInfoSet, nullptr, nullptr, 0, &deviceInterfaceData)) {
        // Get required buffer size
        DWORD requiredSize = 0;
        SetupDiGetDeviceInterfaceDetail(deviceInfoSet, &deviceInterfaceData,
                                       nullptr, 0, &requiredSize, nullptr);

        if (requiredSize > 0) {
            // Allocate buffer and get device path
            std::vector<BYTE> buffer(requiredSize);
            PSP_DEVICE_INTERFACE_DETAIL_DATA pDetailData =
                reinterpret_cast<PSP_DEVICE_INTERFACE_DETAIL_DATA>(buffer.data());
            pDetailData->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA);

            if (SetupDiGetDeviceInterfaceDetail(deviceInfoSet, &deviceInterfaceData,
                                               pDetailData, requiredSize, nullptr, nullptr)) {
                std::wstring devicePath = pDetailData->DevicePath;
                SetupDiDestroyDeviceInfoList(deviceInfoSet);
                return devicePath;
            }
        }
    }

    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    return L"";
}

RS485Error RS485DriverInterface::sendDeviceIoControl(DWORD ioControlCode,
                                                    const void* inputBuffer, DWORD inputBufferSize,
                                                    void* outputBuffer, DWORD outputBufferSize,
                                                    DWORD* bytesReturned)
{
    if (!isDriverOpen()) {
        return RS485Error::DEVICE_NOT_OPEN;
    }

    DWORD localBytesReturned = 0;
    if (!bytesReturned) {
        bytesReturned = &localBytesReturned;
    }

    BOOL result = DeviceIoControl(
        m_hDriver,
        ioControlCode,
        const_cast<void*>(inputBuffer),
        inputBufferSize,
        outputBuffer,
        outputBufferSize,
        bytesReturned,
        nullptr
    );

    if (!result) {
        DWORD error = GetLastError();
        switch (error) {
            case ERROR_INVALID_FUNCTION:
                return RS485Error::INVALID_FUNCTION_CODE;
            case ERROR_INVALID_PARAMETER:
                return RS485Error::INVALID_PARAMETER;
            case ERROR_INSUFFICIENT_BUFFER:
                return RS485Error::INSUFFICIENT_BUFFER;
            case ERROR_TIMEOUT:
                return RS485Error::COMMUNICATION_TIMEOUT;
            case ERROR_DEVICE_NOT_CONNECTED:
                return RS485Error::DEVICE_NOT_FOUND;
            case ERROR_ACCESS_DENIED:
                return RS485Error::INSUFFICIENT_PRIVILEGES;
            default:
                return RS485Error::DRIVER_ERROR;
        }
    }

    return RS485Error::SUCCESS;
}

RS485Error RS485DriverInterface::validateCommandKey(const std::string& commandKey) const
{
    if (commandKey.length() != 4) {
        return RS485Error::INVALID_PARAMETER;
    }

    // Check if all characters are printable ASCII
    for (char c : commandKey) {
        if (c < 0x20 || c > 0x7E) {
            return RS485Error::INVALID_PARAMETER;
        }
    }

    return RS485Error::SUCCESS;
}

RS485Error RS485DriverInterface::validateSlaveAddress(uint8_t slaveAddress) const
{
    if (slaveAddress < 1 || slaveAddress > 31) {
        return RS485Error::INVALID_DEVICE_ADDRESS;
    }

    return RS485Error::SUCCESS;
}
