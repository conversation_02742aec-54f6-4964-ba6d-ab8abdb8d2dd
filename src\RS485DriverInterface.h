#ifndef RS485_DRIVER_INTERFACE_H
#define RS485_DRIVER_INTERFACE_H

#include "RS485Types.h"
#include <windows.h>
#include <string>
#include <vector>

/**
 * @brief User-mode interface to RS485 UMDF driver
 * 
 * This class provides the high-level API that communicates with the UMDF driver
 * using DeviceIoControl calls. It abstracts away the driver communication details
 * and provides the five API categories as specified in the design document.
 */
class RS485DriverInterface {
public:
    /**
     * @brief Constructor
     */
    RS485DriverInterface();

    /**
     * @brief Destructor
     */
    ~RS485DriverInterface();

    // ===== ERROR HANDLE API (Management Functions) =====
    
    /**
     * @brief Open connection to RS485 driver
     * @param devicePath Device path (optional, auto-detected if empty)
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error openDriver(const std::wstring& devicePath = L"");

    /**
     * @brief Close connection to RS485 driver
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error closeDriver();

    /**
     * @brief Check if driver connection is open
     * @return true if connected to driver
     */
    bool isDriverOpen() const;

    /**
     * @brief Get buffer status from driver
     * @param status Output structure for buffer status
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getBufferStatus(BufferStatus& status);

    /**
     * @brief Clear communication buffers in driver
     * @param bufferType Type of buffer to clear
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error clearBuffer(BufferType bufferType = BufferType::BOTH);

    /**
     * @brief Get hardware status from driver
     * @param status Output structure for hardware status
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getHardwareStatus(HardwareStatus& status);

    /**
     * @brief Get performance metrics from driver
     * @param metrics Output structure for performance metrics
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error getPerformanceMetrics(PerformanceMetrics& metrics);

    /**
     * @brief Get error description string
     * @param error Error code
     * @return Human-readable error description
     */
    const char* getErrorString(RS485Error error) const;

    // ===== MASTER BROADCASTING API (S-series Commands) =====
    
    /**
     * @brief Configure system settings via driver
     * @param commandKey Command identifier (e.g., "S001", "S002")
     * @param value Configuration value
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error configureSystemSettings(const std::string& commandKey, uint64_t value);

    // ===== MASTER ASSIGN DATA API (U-series and W-series Commands) =====
    
    /**
     * @brief Configure user settings via driver
     * @param commandKey Command identifier (e.g., "U001", "U002")
     * @param value Configuration value
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error configureUserSettings(const std::string& commandKey, uint64_t value);

    /**
     * @brief AI model data operation via driver
     * @param slaveAddress Target slave device address
     * @param address Memory address in FRAM
     * @param data Data buffer for read/write operations
     * @param isWrite true for write operation, false for read
     * @param length Number of bytes to read/write
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error modelDataOperation(uint8_t slaveAddress, uint32_t address, 
                                 uint8_t* data, bool isWrite, size_t length);

    // ===== MASTER REQUEST API (A-series Commands) =====
    
    /**
     * @brief Request data from slave device via driver
     * @param slaveAddress Target slave device address
     * @param dataKey Data identifier (e.g., "A001", "A002")
     * @param responseData Output buffer for response data
     * @param timeout Timeout in milliseconds
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error requestData(uint8_t slaveAddress, const std::string& dataKey, 
                          std::vector<uint8_t>& responseData, uint32_t timeout = 1000);

    // ===== SLAVE RESPONSE API (Response Handling) =====
    
    /**
     * @brief Receive slave response via driver
     * @param slaveAddress Expected slave device address (0 for any)
     * @param responseData Output buffer for response data
     * @param timeout Timeout in milliseconds
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                   uint32_t timeout = 1000);

    // ===== UTILITY FUNCTIONS =====
    
    /**
     * @brief Initialize the driver interface
     * @return RS485Error::SUCCESS if successful
     */
    RS485Error Initialize();

    /**
     * @brief Set current slave address for U-series commands
     * @param address Slave address (1-31)
     */
    void setCurrentSlaveAddress(uint8_t address);

    /**
     * @brief Get current slave address
     * @return Current slave address
     */
    uint8_t getCurrentSlaveAddress() const;

    /**
     * @brief Find RS485 driver device path
     * @return Device path string, empty if not found
     */
    static std::wstring findDriverDevicePath();

private:
    // IOCTL structures (matching driver definitions)
    typedef struct _RS485_COMMAND_INPUT {
        CHAR CommandKey[4];     // Command identifier
        UINT64 Value;           // Command value
        UINT8 SlaveAddress;     // Target slave address
        UINT8 Reserved[3];      // Padding
    } RS485_COMMAND_INPUT;

    typedef struct _RS485_REQUEST_INPUT {
        CHAR DataKey[4];        // Data identifier
        UINT8 SlaveAddress;     // Target slave address
        UINT32 Timeout;         // Timeout in milliseconds
        UINT8 Reserved[3];      // Padding
    } RS485_REQUEST_INPUT;

    typedef struct _RS485_RESPONSE_OUTPUT {
        UINT8 SlaveAddress;     // Source slave address
        UINT8 PayloadData[12];  // Response payload data
        UINT32 DataLength;      // Actual data length
        UINT8 Reserved[3];      // Padding
    } RS485_RESPONSE_OUTPUT;

    typedef struct _RS485_MODEL_DATA_INPUT {
        UINT8 SlaveAddress;     // Target slave address
        UINT32 Address;         // Memory address
        UINT32 Length;          // Data length
        BOOLEAN IsWrite;        // Operation type
        UINT8 Data[256];        // Data buffer
    } RS485_MODEL_DATA_INPUT;

    // IOCTL codes (matching driver definitions)
    static constexpr DWORD IOCTL_RS485_CONFIGURE_SYSTEM    = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_CONFIGURE_USER      = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_REQUEST_DATA        = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_RECEIVE_RESPONSE    = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_GET_BUFFER_STATUS   = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_CLEAR_BUFFER        = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_GET_HARDWARE_STATUS = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS);
    static constexpr DWORD IOCTL_RS485_MODEL_DATA_OP       = CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS);

    // Helper methods
    RS485Error sendDeviceIoControl(DWORD ioControlCode, 
                                  const void* inputBuffer, DWORD inputBufferSize,
                                  void* outputBuffer, DWORD outputBufferSize,
                                  DWORD* bytesReturned = nullptr);

    RS485Error validateCommandKey(const std::string& commandKey) const;
    RS485Error validateSlaveAddress(uint8_t slaveAddress) const;

    // Member variables
    HANDLE m_hDriver;                    // Handle to driver device
    std::wstring m_devicePath;           // Driver device path
    uint8_t m_currentSlaveAddress;       // Current slave address for U-series commands
    bool m_isOpen;                       // Connection status

    // Prevent copying
    RS485DriverInterface(const RS485DriverInterface&) = delete;
    RS485DriverInterface& operator=(const RS485DriverInterface&) = delete;
};

#endif // RS485_DRIVER_INTERFACE_H
