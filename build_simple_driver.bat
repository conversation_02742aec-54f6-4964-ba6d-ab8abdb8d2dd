@echo off
echo ========================================
echo   Simple UMDF Driver Build
echo ========================================
echo.

REM Create build directories
if not exist build mkdir build
if not exist build\driver mkdir build\driver

REM Check for compiler
where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Setting up Visual Studio environment...
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo ERROR: Visual Studio not found!
        pause
        exit /b 1
    )
)

echo Compiler ready.
echo.

cd driver

echo Compiling simple UMDF driver...
cl /c /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
   /I..\include ^
   SimpleUMDFDriver.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    cd ..
    pause
    exit /b 1
)

echo Linking driver DLL...
link /DLL /DEF:RS485Filter.def /OUT:..\build\driver\RS485Filter.dll ^
     SimpleUMDFDriver.obj ^
     kernel32.lib user32.lib setupapi.lib advapi32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo Build SUCCESS!
echo ========================================
echo.

if exist build\driver\RS485Filter.dll (
    echo ✓ Driver DLL: build\driver\RS485Filter.dll
    dir build\driver\RS485Filter.dll | find "RS485Filter.dll"
    
    echo ✓ Copying INF file...
    copy driver\RS485Filter.inf build\driver\
    
    echo.
    echo Files created:
    echo - build\driver\RS485Filter.dll (Simplified UMDF Driver)
    echo - build\driver\RS485Filter.inf (Installation file)
    echo.
    echo This driver demonstrates UMDF concepts and can process:
    echo ✓ S001, S002 system configuration commands
    echo ✓ U001-U006 user configuration commands  
    echo ✓ A001-A005 data request commands
    echo ✓ Buffer management and status queries
    echo ✓ IOCTL-based communication interface
    echo.
    echo Ready for testing!
    
) else (
    echo ✗ Build failed - DLL not created
)

echo.
echo Press any key to exit...
pause >nul
