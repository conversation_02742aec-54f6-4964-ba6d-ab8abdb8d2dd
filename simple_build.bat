@echo off
echo ========================================
echo    RS485 Communication Application
echo         Simple Build Script
echo ========================================
echo.

REM Try to find and use any available C++ compiler
echo Checking for available compilers...

REM Check for cl.exe (MSVC)
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Found MSVC compiler
    goto :build_with_msvc
)

REM Check for g++.exe (MinGW)
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Found MinGW compiler
    goto :build_with_mingw
)

REM Check for clang++.exe
where clang++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Found Clang compiler
    goto :build_with_clang
)

echo ERROR: No C++ compiler found!
echo Please install one of the following:
echo - Visual Studio with C++ tools
echo - MinGW-w64
echo - Clang
pause
exit /b 1

:build_with_msvc
echo Building with MSVC...
cd build
cl /EHsc /std:c++17 /I..\include ^
   ..\src\main.cpp ..\src\RS485Driver.cpp ..\src\FTDIInstaller.cpp ^
   /Fe:RS485_Communication_Application.exe ^
   setupapi.lib newdev.lib advapi32.lib user32.lib kernel32.lib version.lib
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
) else (
    echo Build failed!
)
goto :end

:build_with_mingw
echo Building with MinGW...
cd build
g++ -std=c++17 -I../include ^
    ../src/main.cpp ../src/RS485Driver.cpp ../src/FTDIInstaller.cpp ^
    -o RS485_Communication_Application.exe ^
    -lsetupapi -lnewdev -ladvapi32 -luser32 -lkernel32 -lversion
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
) else (
    echo Build failed!
)
goto :end

:build_with_clang
echo Building with Clang...
cd build
clang++ -std=c++17 -I../include ^
    ../src/main.cpp ../src/RS485Driver.cpp ../src/FTDIInstaller.cpp ^
    -o RS485_Communication_Application.exe ^
    -lsetupapi -lnewdev -ladvapi32 -luser32 -lkernel32 -lversion
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
) else (
    echo Build failed!
)
goto :end

:end
echo.
echo Press any key to exit...
pause >nul
