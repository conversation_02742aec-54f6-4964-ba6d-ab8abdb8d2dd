@echo off
echo ========================================
echo   Real UMDF Driver Build Script
echo ========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Administrator privileges recommended for driver development
    echo.
)

REM Set up Visual Studio environment
set "VS_PATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
) else (
    echo ERROR: Visual Studio not found!
    echo Please install Visual Studio 2019 or 2022 with C++ development tools.
    pause
    exit /b 1
)

echo Setting up Visual Studio environment...
call "%VS_PATH%"

REM Check for WDK
set "WDK_PATH="
if exist "C:\Program Files (x86)\Windows Kits\10" (
    set "WDK_PATH=C:\Program Files (x86)\Windows Kits\10"
) else if exist "C:\Program Files\Windows Kits\10" (
    set "WDK_PATH=C:\Program Files\Windows Kits\10"
) else (
    echo ERROR: Windows Driver Kit (WDK) not found!
    echo Please install WDK 10 from Microsoft.
    pause
    exit /b 1
)

echo Found WDK at: %WDK_PATH%

REM Find WDK version
set WDK_VERSION=
for /d %%i in ("%WDK_PATH%\Include\10.*") do (
    set WDK_VERSION=%%~ni
)

if "%WDK_VERSION%"=="" (
    echo ERROR: Could not determine WDK version!
    pause
    exit /b 1
)

echo WDK Version: %WDK_VERSION%

REM Create build directories
if not exist build mkdir build
if not exist build\driver mkdir build\driver

echo.
echo ========================================
echo Building UMDF Driver
echo ========================================
echo.

REM Try to build with MSBuild first
cd driver

echo Attempting MSBuild...
msbuild RS485Filter.vcxproj /p:Configuration=Release /p:Platform=x64 /p:WindowsTargetPlatformVersion=%WDK_VERSION% /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo ✓ MSBuild successful!
    goto :build_success
)

echo MSBuild failed, trying manual compilation...

REM Manual compilation approach
echo Setting up WDK environment...
set INCLUDE=%WDK_PATH%\Include\%WDK_VERSION%\um;%WDK_PATH%\Include\%WDK_VERSION%\shared;%WDK_PATH%\Include\%WDK_VERSION%\km;%INCLUDE%
set LIB=%WDK_PATH%\Lib\%WDK_VERSION%\um\x64;%WDK_PATH%\Lib\%WDK_VERSION%\km\x64;%LIB%

echo Compiling driver sources...
cl /c /EHsc /std:c++17 /DUNICODE /D_UNICODE /DWIN32_LEAN_AND_MEAN /DUMDF_USING_NTSTATUS ^
   /I..\include /I"%WDK_PATH%\Include\%WDK_VERSION%\um" ^
   /I"%WDK_PATH%\Include\%WDK_VERSION%\shared" ^
   /I"%WDK_PATH%\Include\%WDK_VERSION%\km" ^
   /Fo..\build\driver\ RS485FilterDriver.cpp dllsup.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    cd ..
    pause
    exit /b 1
)

echo Linking driver DLL...
link /DLL /DEF:RS485Filter.def /OUT:..\build\driver\RS485Filter.dll ^
     /LIBPATH:"%WDK_PATH%\Lib\%WDK_VERSION%\um\x64" ^
     /LIBPATH:"%WDK_PATH%\Lib\%WDK_VERSION%\km\x64" ^
     ..\build\driver\RS485FilterDriver.obj ..\build\driver\dllsup.obj ^
     kernel32.lib user32.lib setupapi.lib cfgmgr32.lib

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    cd ..
    pause
    exit /b 1
)

:build_success
cd ..

echo.
echo ========================================
echo Build Results
echo ========================================
echo.

if exist build\driver\RS485Filter.dll (
    echo ✓ UMDF Driver: build\driver\RS485Filter.dll
    dir build\driver\RS485Filter.dll | find "RS485Filter.dll"
) else if exist x64\Release\RS485Filter.dll (
    echo ✓ UMDF Driver: x64\Release\RS485Filter.dll
    copy x64\Release\RS485Filter.dll build\driver\
    dir build\driver\RS485Filter.dll | find "RS485Filter.dll"
) else (
    echo ✗ Driver DLL not found!
    echo Check the build output for errors.
    goto :error_exit
)

if exist driver\RS485Filter.inf (
    echo ✓ Driver INF: driver\RS485Filter.inf
    copy driver\RS485Filter.inf build\driver\
) else (
    echo ✗ Driver INF not found!
)

echo.
echo ========================================
echo Next Steps
echo ========================================
echo.
echo 1. Sign the driver (for production):
echo    signtool sign /v /s my /n "Your Certificate" build\driver\RS485Filter.dll
echo.
echo 2. Install the driver:
echo    pnputil /add-driver build\driver\RS485Filter.inf /install
echo.
echo 3. Enable test signing (if unsigned):
echo    bcdedit /set testsigning on
echo.
echo 4. Test with the user application
echo.
goto :end

:error_exit
echo.
echo ========================================
echo Build FAILED!
echo ========================================
echo.
echo Troubleshooting:
echo 1. Ensure WDK 10 is properly installed
echo 2. Check Visual Studio C++ tools installation
echo 3. Verify administrator privileges
echo 4. Review error messages above
echo.

:end
echo Press any key to exit...
pause >nul
