cmake_minimum_required(VERSION 3.16)
project(RS485_Communication_Application)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release by default
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Platform check
if(NOT WIN32)
    message(FATAL_ERROR "This project is designed for Windows only")
endif()

# Include directories
include_directories(include)
include_directories(resources)

# Source files
set(SOURCES
    src/main.cpp
    src/RS485Driver.cpp
    src/FTDIInstaller.cpp
)

# Header files
set(HEADERS
    include/RS485Types.h
    include/RS485DataFormat.h
    include/CRC8Calculator.h
    include/RS485Driver.h
    include/FTDIInstaller.h
    include/resource.h
)

# Resource files
set(RESOURCES
    resources/application.rc
    resources/application.manifest
)

# Create executable
add_executable(${PROJECT_NAME} WIN32 ${SOURCES} ${HEADERS} ${RESOURCES})

# Set target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "RS485_Communication_Application"
    WIN32_EXECUTABLE TRUE
)

# Link libraries
target_link_libraries(${PROJECT_NAME}
    setupapi
    newdev
    advapi32
    user32
    kernel32
    version
    cfgmgr32
)

# Compiler-specific options
if(MSVC)
    # MSVC specific options
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4                     # Warning level 4
        /WX-                    # Don't treat warnings as errors
        /permissive-            # Disable non-conforming code
        /Zc:__cplusplus         # Enable correct __cplusplus macro
        /utf-8                  # Use UTF-8 encoding
    )
    
    # MSVC specific definitions
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _WIN32_WINNT=0x0601     # Windows 7 minimum
        WINVER=0x0601
        _UNICODE
        UNICODE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
    )
    
    # Set subsystem to Windows (GUI application)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )
    
elseif(MINGW)
    # MinGW specific options
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall
        -Wextra
        -Wpedantic
    )
    
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _WIN32_WINNT=0x0601
        WINVER=0x0601
        _UNICODE
        UNICODE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
    )
endif()

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _DEBUG
        DEBUG
    )
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        NDEBUG
    )
endif()

# Custom commands for resource preparation
add_custom_target(prepare_resources
    COMMENT "Preparing embedded resources..."
)

# Create output directories
add_custom_command(TARGET ${PROJECT_NAME} PRE_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory
    "${CMAKE_BINARY_DIR}/drivers"
    "${CMAKE_BINARY_DIR}/libs"
    COMMENT "Creating resource directories"
)

# Copy runtime dependencies
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E echo "Build completed successfully"
    COMMENT "Post-build steps"
)

# Installation configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# CPack configuration for creating installers
set(CPACK_PACKAGE_NAME "RS485 Communication Application")
set(CPACK_PACKAGE_VENDOR "Your Company")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "RS485 Communication Application with Embedded FTDI Driver")
set(CPACK_PACKAGE_EXECUTABLES "RS485_Communication_Application" "RS485 Communication Application")

# NSIS specific settings
set(CPACK_GENERATOR "NSIS")
set(CPACK_NSIS_DISPLAY_NAME "RS485 Communication Application")
set(CPACK_NSIS_PACKAGE_NAME "RS485 Communication Application")
set(CPACK_NSIS_CONTACT "<EMAIL>")
set(CPACK_NSIS_HELP_LINK "https://yourcompany.com/support")
set(CPACK_NSIS_URL_INFO_ABOUT "https://yourcompany.com")
set(CPACK_NSIS_MODIFY_PATH ON)
set(CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL ON)

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "=== RS485 Communication Application Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Target: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "Output: ${CMAKE_BINARY_DIR}/${CMAKE_BUILD_TYPE}/RS485_Communication_Application.exe")
message(STATUS "=====================================================")
message(STATUS "")
