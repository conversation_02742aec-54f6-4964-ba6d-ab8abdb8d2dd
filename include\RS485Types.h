#ifndef RS485_TYPES_H
#define RS485_TYPES_H

#include <windows.h>
#include <cstdint>
#include <string>
#include <vector>
#include <functional>

// RS485 Error Codes
enum class RS485Error : uint32_t {
    SUCCESS = 0,
    DEVICE_NOT_FOUND,
    DEVICE_ALREADY_OPEN,
    DEVICE_NOT_OPEN,
    INVALID_PARAMETER,
    INSUFFICIENT_BUFFER,
    COMMUNICATION_TIMEOUT,
    CRC_ERROR,
    FRAME_ERROR,
    BUFFER_OVERFLOW,
    DRIVER_ERROR,
    INSUFFICIENT_PRIVILEGES,
    INSTALLATION_FAILED,
    INVALID_FUNCTION_CODE,
    INVALID_DEVICE_ADDRESS,
    HARDWARE_ERROR,
    UNKNOWN_ERROR
};

// Buffer Types
enum class BufferType {
    UPLINK,
    DOWNLINK,
    BOTH
};

// Buffer Overflow Policies
enum class BufferOverflowPolicy {
    DISCARD_OLDEST,
    DISCARD_NEWEST,
    TRIGGER_ERROR
};

// ZES Protocol Frame Structure (16 bytes)
#pragma pack(push, 1)
struct RS485Frame {
    uint8_t header;        // 0xAA (1 byte)
    uint8_t id_byte;       // Function code + device address (1 byte)
    uint8_t payload[12];   // Key (4 bytes) + Value (8 bytes)
    uint8_t crc8;          // CRC checksum (1 byte)
    uint8_t trailer;       // 0x0D (1 byte)
};
#pragma pack(pop)

// Function Codes
enum class FunctionCode : uint8_t {
    RESEND_REQUEST = 0b000,     // Re-send request (Both use)
    RESPONSE_REQUEST = 0b001,   // Response to Request (Slave use)
    RESPONSE_ASSIGN = 0b010,    // Response to Assign (Slave use)
    ASSIGN_DATA = 0b111,        // Assign data (Master use)
    REQUEST_DATA = 0b110        // Request data (Master use)
};

// Device Information
struct DeviceInfo {
    std::wstring devicePath;
    std::wstring description;
    std::wstring serialNumber;
    uint32_t vendorId;
    uint32_t productId;
    bool isConnected;
};

// Port Information
struct PortInfo {
    std::wstring portName;
    uint32_t baudRate;
    uint8_t dataBits;
    uint8_t stopBits;
    uint8_t parity;
    bool isOpen;
};

// Buffer Status
struct BufferStatus {
    uint32_t uplinkUsed;
    uint32_t uplinkCapacity;
    uint32_t downlinkUsed;
    uint32_t downlinkCapacity;
    bool isUplinkFull;
    bool isDownlinkFull;
    double uplinkUsagePercent;
    double downlinkUsagePercent;
};

// Hardware Status
struct HardwareStatus {
    bool isConnected;
    bool isDriverLoaded;
    uint32_t signalStrength;
    uint32_t errorCount;
    uint64_t lastActivityTime;
};

// Performance Metrics
struct PerformanceMetrics {
    uint64_t totalFramesSent;
    uint64_t totalFramesReceived;
    uint64_t totalErrors;
    uint64_t totalRetries;
    double averageResponseTime;
    double throughputBytesPerSecond;
    uint32_t bufferUtilizationPercent;
};

// Line Status
struct LineStatus {
    bool carrierDetect;
    bool dataSetReady;
    bool ringIndicator;
    bool clearToSend;
    bool dataTerminalReady;
    bool requestToSend;
    uint32_t signalLevel;
};

// Callback function types
using ErrorCallbackFn = std::function<void(RS485Error, const std::string&)>;
using BufferThresholdCallbackFn = std::function<void(BufferType, uint32_t)>;
using DataReadyCallbackFn = std::function<void(uint8_t, const uint8_t*, size_t)>;

// Constants
constexpr uint8_t RS485_FRAME_HEADER = 0xAA;
constexpr uint8_t RS485_FRAME_TRAILER = 0x0D;
constexpr size_t RS485_FRAME_SIZE = 16;
constexpr size_t RS485_PAYLOAD_SIZE = 12;
constexpr size_t RS485_KEY_SIZE = 4;
constexpr size_t RS485_VALUE_SIZE = 8;
constexpr uint32_t RS485_DEFAULT_TIMEOUT = 100; // milliseconds
constexpr uint32_t RS485_MAX_RETRIES = 3;
constexpr uint32_t RS485_UPLINK_BUFFER_SIZE = 5;   // 5 frames
constexpr uint32_t RS485_DOWNLINK_BUFFER_SIZE = 10; // 10 frames

// CRC8 Polynomial: 0x97 = x^8 + x^5 + x^3 + x^2 + x + 1
constexpr uint8_t CRC8_POLYNOMIAL = 0x97;

#endif // RS485_TYPES_H
