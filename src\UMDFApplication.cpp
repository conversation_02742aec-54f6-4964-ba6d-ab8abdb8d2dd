#include <windows.h>
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include "RS485DriverInterface.h"
#include "FTDIInstaller.h"

/**
 * @brief Convert narrow string to wide string
 */
std::wstring stringToWstring(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

/**
 * @brief Display application banner
 */
void displayBanner() {
    std::wcout << L"========================================" << std::endl;
    std::wcout << L"  RS485 UMDF Driver Application" << std::endl;
    std::wcout << L"    Version 1.0.0 (UMDF 2.0)" << std::endl;
    std::wcout << L"========================================" << std::endl;
    std::wcout << std::endl;
}

/**
 * @brief Check and install FTDI driver if needed
 */
bool checkAndInstallFTDIDriver() {
    std::wcout << L"Checking FTDI driver installation..." << std::endl;

    auto installResult = FTDIInstaller::InstallFTDIDriver();
    std::wstring resultMessage = FTDIInstaller::InstallResultToString(installResult);

    switch (installResult) {
        case FTDIInstaller::InstallResult::SUCCESS:
            std::wcout << L"✓ FTDI driver installed successfully!" << std::endl;
            return true;

        case FTDIInstaller::InstallResult::ALREADY_INSTALLED:
            std::wcout << L"✓ FTDI driver already installed and working." << std::endl;
            return true;

        case FTDIInstaller::InstallResult::INSUFFICIENT_PRIVILEGES:
            MessageBoxW(NULL, L"Please run this application as Administrator to install FTDI drivers.\n\nRight-click the executable and select 'Run as administrator'.",
                       L"Administrator Required", MB_OK | MB_ICONERROR);
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;

        default:
            std::wcout << L"✗ " << resultMessage << std::endl;
            return false;
    }
}

/**
 * @brief Test UMDF driver communication
 */
void testUMDFDriverCommunication(RS485DriverInterface& driver) {
    std::wcout << L"\n=== Testing UMDF Driver Communication ===" << std::endl;
    
    // Test buffer status
    std::wcout << L"1. Getting buffer status..." << std::endl;
    BufferStatus bufferStatus;
    RS485Error result = driver.getBufferStatus(bufferStatus);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ Buffer status retrieved successfully" << std::endl;
        std::wcout << L"  Uplink: " << bufferStatus.uplinkUsed << L"/" << bufferStatus.uplinkCapacity 
                  << L" (" << std::fixed << std::setprecision(1) << bufferStatus.uplinkUsagePercent << L"%)" << std::endl;
        std::wcout << L"  Downlink: " << bufferStatus.downlinkUsed << L"/" << bufferStatus.downlinkCapacity 
                  << L" (" << std::fixed << std::setprecision(1) << bufferStatus.downlinkUsagePercent << L"%)" << std::endl;
    } else {
        std::wcout << L"✗ Failed to get buffer status: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Test hardware status
    std::wcout << L"\n2. Getting hardware status..." << std::endl;
    HardwareStatus hwStatus;
    result = driver.getHardwareStatus(hwStatus);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ Hardware status retrieved successfully" << std::endl;
        std::wcout << L"  Connected: " << (hwStatus.isConnected ? L"Yes" : L"No") << std::endl;
        std::wcout << L"  Driver loaded: " << (hwStatus.isDriverLoaded ? L"Yes" : L"No") << std::endl;
        std::wcout << L"  Signal strength: " << hwStatus.signalStrength << L"%" << std::endl;
        std::wcout << L"  Error count: " << hwStatus.errorCount << std::endl;
    } else {
        std::wcout << L"✗ Failed to get hardware status: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
}

/**
 * @brief Test RS485 commands via UMDF driver
 */
void testRS485Commands(RS485DriverInterface& driver) {
    std::wcout << L"\n=== Testing RS485 Commands via UMDF Driver ===" << std::endl;
    
    // Test S001 command (set slave address)
    std::wcout << L"1. Testing S001 command (set slave address to 5)..." << std::endl;
    RS485Error result = driver.configureSystemSettings("S001", 5);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ S001 command sent successfully via UMDF driver" << std::endl;
        std::wcout << L"  Current slave address: " << static_cast<int>(driver.getCurrentSlaveAddress()) << std::endl;
    } else {
        std::wcout << L"✗ S001 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Wait for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test S002 command (set baud rate)
    std::wcout << L"\n2. Testing S002 command (set baud rate to 115200)..." << std::endl;
    result = driver.configureSystemSettings("S002", 115200);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ S002 command sent successfully via UMDF driver" << std::endl;
    } else {
        std::wcout << L"✗ S002 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Wait for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test U001 command (set SEL detection threshold)
    std::wcout << L"\n3. Testing U001 command (set SEL threshold to 250mA)..." << std::endl;
    result = driver.configureUserSettings("U001", 250);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ U001 command sent successfully via UMDF driver" << std::endl;
    } else {
        std::wcout << L"✗ U001 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
    
    // Wait for processing
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Test A001 command (request SEL event log)
    std::wcout << L"\n4. Testing A001 command (request SEL event log)..." << std::endl;
    std::vector<uint8_t> responseData;
    result = driver.requestData(5, "A001", responseData, 1000);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"✓ A001 command sent successfully via UMDF driver" << std::endl;
        if (!responseData.empty()) {
            std::wcout << L"  Response data (" << responseData.size() << L" bytes): ";
            for (uint8_t byte : responseData) {
                std::wcout << std::hex << std::uppercase << std::setw(2) << std::setfill(L'0') 
                          << static_cast<int>(byte) << L" ";
            }
            std::wcout << std::dec << std::endl;
        } else {
            std::wcout << L"  No response data received (timeout or no slave device)" << std::endl;
        }
    } else {
        std::wcout << L"✗ A001 command failed: " << stringToWstring(driver.getErrorString(result)) << std::endl;
    }
}

/**
 * @brief Interactive command loop
 */
void runInteractiveLoop(RS485DriverInterface& driver) {
    std::wcout << L"\n=== Interactive Command Mode ===" << std::endl;
    std::wcout << L"Commands:" << std::endl;
    std::wcout << L"  's' - Show status" << std::endl;
    std::wcout << L"  't' - Test commands" << std::endl;
    std::wcout << L"  'c' - Clear buffers" << std::endl;
    std::wcout << L"  'q' - Quit" << std::endl;
    std::wcout << L"Enter command: ";
    
    char input;
    while (std::cin >> input) {
        switch (input) {
            case 'q':
            case 'Q':
                std::wcout << L"Exiting..." << std::endl;
                return;
                
            case 's':
            case 'S':
                testUMDFDriverCommunication(driver);
                break;
                
            case 't':
            case 'T':
                testRS485Commands(driver);
                break;
                
            case 'c':
            case 'C':
                {
                    std::wcout << L"Clearing buffers..." << std::endl;
                    RS485Error result = driver.clearBuffer(BufferType::BOTH);
                    if (result == RS485Error::SUCCESS) {
                        std::wcout << L"✓ Buffers cleared successfully" << std::endl;
                    } else {
                        std::wcout << L"✗ Failed to clear buffers: " << stringToWstring(driver.getErrorString(result)) << std::endl;
                    }
                }
                break;
                
            default:
                std::wcout << L"Unknown command. Try 's', 't', 'c', or 'q'." << std::endl;
                break;
        }
        
        std::wcout << L"\nEnter command: ";
    }
}

/**
 * @brief Main application entry point
 */
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    UNREFERENCED_PARAMETER(hInstance);
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    // Allocate console for output
    AllocConsole();
    FILE* pCout;
    freopen_s(&pCout, "CONOUT$", "w", stdout);
    FILE* pCin;
    freopen_s(&pCin, "CONIN$", "r", stdin);
    FILE* pCerr;
    freopen_s(&pCerr, "CONOUT$", "w", stderr);
    
    // Set console title
    SetConsoleTitleW(L"RS485 UMDF Driver Application");
    
    // Set locale for proper Unicode output
    std::locale::global(std::locale(""));
    std::wcout.imbue(std::locale());
    
    try {
        // Display banner
        displayBanner();
        
        // Check and install FTDI driver
        if (!checkAndInstallFTDIDriver()) {
            std::wcout << L"\nPress any key to exit..." << std::endl;
            std::cin.get();
            return 1;
        }
        
        // Initialize RS485 driver interface
        std::wcout << L"\nInitializing RS485 UMDF driver interface..." << std::endl;
        RS485DriverInterface driver;
        
        RS485Error initResult = driver.Initialize();
        if (initResult != RS485Error::SUCCESS) {
            std::wcout << L"✗ Failed to initialize RS485 driver interface: " 
                      << stringToWstring(driver.getErrorString(initResult)) << std::endl;
            std::wcout << L"\nNote: This may be expected if the UMDF driver is not yet installed." << std::endl;
            std::wcout << L"The UMDF driver needs to be compiled and installed separately." << std::endl;
            std::wcout << L"\nPress any key to exit..." << std::endl;
            std::cin.get();
            return 1;
        }
        
        std::wcout << L"✓ RS485 UMDF driver interface initialized successfully!" << std::endl;
        
        // Test driver communication
        testUMDFDriverCommunication(driver);
        
        // Test RS485 commands
        testRS485Commands(driver);
        
        // Run interactive loop
        runInteractiveLoop(driver);
        
        // Cleanup
        driver.closeDriver();
        std::wcout << L"Application closed successfully." << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::wcout << L"Exception occurred: " << stringToWstring(e.what()) << std::endl;
        MessageBoxA(NULL, e.what(), "Application Error", MB_OK | MB_ICONERROR);
        return 1;
    } catch (...) {
        std::wcout << L"Unknown exception occurred." << std::endl;
        MessageBoxW(NULL, L"An unknown error occurred.", L"Application Error", MB_OK | MB_ICONERROR);
        return 1;
    }
}
