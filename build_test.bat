@echo off
echo ========================================
echo   RS485 UMDF Concept Testing
echo ========================================
echo.

if not exist build mkdir build

echo Compiling UMDF concept test...

REM Try MinGW first
g++.exe --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW G++ Compiler
    g++ -std=c++17 -DWIN32 -D_WINDOWS -DUNICODE -D_UNICODE ^
        -I include ^
        src/TestUMDFConcepts.cpp ^
        -o build/TestUMDFConcepts.exe ^
        -static-libgcc -static-libstdc++
    goto :check_result
)

REM Try MSVC
cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Using Microsoft Visual C++ Compiler
    cl /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
       /I include ^
       src/TestUMDFConcepts.cpp ^
       /Fe:build/TestUMDFConcepts.exe
    goto :check_result
)

echo ERROR: No C++ compiler found!
pause
exit /b 1

:check_result
if exist build\TestUMDFConcepts.exe (
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo ========================================
    echo.
    echo Output: build\TestUMDFConcepts.exe
    echo.
    echo Starting UMDF concept test...
    echo.
    build\TestUMDFConcepts.exe
) else (
    echo.
    echo ========================================
    echo Build FAILED!
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
