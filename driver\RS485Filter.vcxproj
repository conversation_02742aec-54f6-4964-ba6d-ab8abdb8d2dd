<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12345678-1234-5678-9012-123456789012}</ProjectGuid>
    <TemplateGuid>{dd38f7fc-d7bd-488b-9242-7d8754cde80d}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">x64</Platform>
    <RootNamespace>RS485Filter</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsUserModeDriver10.0</PlatformToolset>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <DriverType>UMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsUserModeDriver10.0</PlatformToolset>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <DriverType>UMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <TargetName>RS485Filter</TargetName>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <TargetName>RS485Filter</TargetName>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarnAsError>false</WarnAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories);..\include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDOWS;_USRDLL;%(PreprocessorDefinitions);UMDF_USING_NTSTATUS</PreprocessorDefinitions>
      <WppEnabled>false</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>false</WppKernelMode>
    </ClCompile>
    <Midl>
      <MkTypLibCompatible>No</MkTypLibCompatible>
      <TargetEnvironment>X64</TargetEnvironment>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
      <GenerateStublessProxies>true</GenerateStublessProxies>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <DllDataFileName>dlldata.c</DllDataFileName>
      <ValidateAllParameters>true</ValidateAllParameters>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>RS485Filter.def</ModuleDefinitionFile>
      <AdditionalDependencies>$(DDK_LIB_PATH)\wudfddi.lib;$(DDK_LIB_PATH)\wudfx02000.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <Inf>
      <TimeStamp>*</TimeStamp>
    </Inf>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarnAsError>false</WarnAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>MaxSpeed</Optimization>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories);..\include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDOWS;_USRDLL;%(PreprocessorDefinitions);UMDF_USING_NTSTATUS</PreprocessorDefinitions>
      <WppEnabled>false</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>false</WppKernelMode>
    </ClCompile>
    <Midl>
      <MkTypLibCompatible>No</MkTypLibCompatible>
      <TargetEnvironment>X64</TargetEnvironment>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
      <GenerateStublessProxies>true</GenerateStublessProxies>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <DllDataFileName>dlldata.c</DllDataFileName>
      <ValidateAllParameters>true</ValidateAllParameters>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>RS485Filter.def</ModuleDefinitionFile>
      <AdditionalDependencies>$(DDK_LIB_PATH)\wudfddi.lib;$(DDK_LIB_PATH)\wudfx02000.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <Inf>
      <TimeStamp>*</TimeStamp>
    </Inf>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="RS485FilterDriver.h" />
    <ClInclude Include="..\include\RS485Types.h" />
    <ClInclude Include="..\include\CRC8Calculator.h" />
    <ClInclude Include="..\include\RS485DataFormat.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="RS485FilterDriver.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RS485Filter.def" />
  </ItemGroup>
  <ItemGroup>
    <Inf Include="RS485Filter.inf" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
